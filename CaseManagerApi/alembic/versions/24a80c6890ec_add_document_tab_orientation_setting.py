"""add document tab orientation setting

Revision ID: 24a80c6890ec
Revises: 3ca40053ed7b
Create Date: 2025-08-06 20:26:12.268475

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '24a80c6890ec'
down_revision: Union[str, None] = '3ca40053ed7b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_settings', sa.Column('document_tabs_vertical', sa.<PERSON>(), nullable=False, default=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_settings', 'document_tabs_vertical')
    # ### end Alembic commands ###
