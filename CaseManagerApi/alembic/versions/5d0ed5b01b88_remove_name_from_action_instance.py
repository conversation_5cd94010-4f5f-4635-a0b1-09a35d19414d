"""remove name from action instance

Revision ID: 5d0ed5b01b88
Revises: 4b0ba319d03f
Create Date: 2025-07-02 19:48:19.998931

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5d0ed5b01b88'
down_revision: Union[str, None] = '4b0ba319d03f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('action_instances', 'name')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('action_instances', sa.Column('name', sa.VARCHAR(length=50), autoincrement=False, nullable=False))
    # ### end Alembic commands ###
