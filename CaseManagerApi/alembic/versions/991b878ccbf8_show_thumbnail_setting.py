"""show thumbnail setting

Revision ID: 991b878ccbf8
Revises: a23c5526617f
Create Date: 2025-07-23 22:29:09.147422

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '991b878ccbf8'
down_revision: Union[str, None] = 'a23c5526617f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_settings', sa.Column('show_thumbnails', sa.<PERSON>(), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_settings', 'show_thumbnails')
    # ### end Alembic commands ###
