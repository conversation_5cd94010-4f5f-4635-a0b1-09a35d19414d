"""add action

Revision ID: 17ae79fe9bc3
Revises: b4189db4e4ec
Create Date: 2025-06-26 20:43:04.822707

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '17ae79fe9bc3'
down_revision: Union[str, None] = 'b4189db4e4ec'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('action_groups',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False),
    sa.Column('description', sa.String(length=200), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_action_groups_id'), 'action_groups', ['id'], unique=False)
    op.create_table('actions',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False),
    sa.Column('description', sa.String(length=200), nullable=False),
    sa.Column('group_id', sa.UUID(), nullable=False),
    sa.ForeignKeyConstraint(['group_id'], ['action_groups.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_actions_id'), 'actions', ['id'], unique=False)
    op.create_table('action_parameters',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False),
    sa.Column('description', sa.String(length=200), nullable=False),
    sa.Column('type', sa.String(length=50), nullable=False),
    sa.Column('input', sa.Boolean(), nullable=False),
    sa.Column('default_value', sa.String(length=200), nullable=True),
    sa.Column('action_id', sa.UUID(), nullable=False),
    sa.ForeignKeyConstraint(['action_id'], ['actions.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_action_parameters_id'), 'action_parameters', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_action_parameters_id'), table_name='action_parameters')
    op.drop_table('action_parameters')
    op.drop_index(op.f('ix_actions_id'), table_name='actions')
    op.drop_table('actions')
    op.drop_index(op.f('ix_action_groups_id'), table_name='action_groups')
    op.drop_table('action_groups')
    # ### end Alembic commands ###
