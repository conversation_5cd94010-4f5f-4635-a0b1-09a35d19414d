"""change value column

Revision ID: 4b0ba319d03f
Revises: 5bde5724b9db
Create Date: 2025-07-02 18:39:57.921937

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4b0ba319d03f'
down_revision: Union[str, None] = '5bde5724b9db'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('action_instance_parameter_bindings', 'value',
               existing_type=sa.VARCHAR(length=200),
               type_=sa.String(length=500),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('action_instance_parameter_bindings', 'value',
               existing_type=sa.String(length=500),
               type_=sa.VARCHAR(length=200),
               nullable=False)
    # ### end Alembic commands ###
