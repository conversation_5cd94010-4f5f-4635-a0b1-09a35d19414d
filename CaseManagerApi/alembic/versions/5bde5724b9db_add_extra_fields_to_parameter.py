"""add extra fields to parameter

Revision ID: 5bde5724b9db
Revises: a60161983c76
Create Date: 2025-07-02 17:52:24.290158

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5bde5724b9db'
down_revision: Union[str, None] = 'a60161983c76'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('action_parameters', sa.Column('required', sa.<PERSON>(), nullable=False))
    op.add_column('action_parameters', sa.Column('min_length', sa.Integer(), nullable=True))
    op.add_column('action_parameters', sa.Column('max_length', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('action_parameters', 'max_length')
    op.drop_column('action_parameters', 'min_length')
    op.drop_column('action_parameters', 'required')
    # ### end Alembic commands ###
