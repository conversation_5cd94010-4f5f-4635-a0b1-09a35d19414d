"""create cases table

Revision ID: 06e4ed25b39f
Revises: 5d0ed5b01b88
Create Date: 2025-07-18 18:53:16.337908

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '06e4ed25b39f'
down_revision: Union[str, None] = '5d0ed5b01b88'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('cases',
    sa.Column('id', sa.String(length=100), nullable=False),
    sa.Column('display_name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.String(length=200), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_cases_id'), 'cases', ['id'], unique=False)
    op.alter_column('documents', 'case_id',
               existing_type=sa.VARCHAR(length=50),
               type_=sa.String(length=100),
               existing_nullable=False)
    op.drop_index(op.f('ix_documents_case_id'), table_name='documents')
    op.create_foreign_key('fk_documents_case_id', 'documents', 'cases', ['case_id'], ['id'])
    op.add_column('transactions', sa.Column('case_id', sa.String(length=100), nullable=False))
    op.drop_constraint(op.f('transactions_document_id_fkey'), 'transactions', type_='foreignkey')
    op.create_foreign_key('fk_transactions_case_id', 'transactions', 'cases', ['case_id'], ['id'])
    op.drop_column('transactions', 'document_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('transactions', sa.Column('document_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False))
    op.drop_constraint('fk_transactions_case_id', 'transactions', type_='foreignkey')
    op.create_foreign_key(op.f('transactions_document_id_fkey'), 'transactions', 'documents', ['document_id'], ['id'])
    op.drop_column('transactions', 'case_id')
    op.drop_constraint('fk_documents_case_id', 'documents', type_='foreignkey')
    op.create_index(op.f('ix_documents_case_id'), 'documents', ['case_id'], unique=False)
    op.alter_column('documents', 'case_id',
               existing_type=sa.String(length=100),
               type_=sa.VARCHAR(length=50),
               existing_nullable=False)
    op.drop_index(op.f('ix_cases_id'), table_name='cases')
    op.drop_table('cases')
    # ### end Alembic commands ###
