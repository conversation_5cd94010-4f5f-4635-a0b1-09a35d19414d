"""change user settings schema

Revision ID: 90f07857405d
Revises: 24a80c6890ec
Create Date: 2025-08-07 12:33:20.112100

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '90f07857405d'
down_revision: Union[str, None] = '24a80c6890ec'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_settings', sa.Column('key', sa.String(length=100), nullable=False))
    op.add_column('user_settings', sa.Column('value', sa.String(length=100), nullable=False))
    op.drop_column('user_settings', 'show_thumbnails')
    op.drop_column('user_settings', 'document_tabs_vertical')
    op.drop_column('user_settings', 'thumbnail_size_px')
    op.drop_column('user_settings', 'language')
    op.drop_column('user_settings', 'theme')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_settings', sa.Column('theme', sa.VARCHAR(length=50), autoincrement=False, nullable=False))
    op.add_column('user_settings', sa.Column('language', sa.VARCHAR(length=5), autoincrement=False, nullable=False))
    op.add_column('user_settings', sa.Column('thumbnail_size_px', sa.INTEGER(), autoincrement=False, nullable=False))
    op.add_column('user_settings', sa.Column('document_tabs_vertical', sa.BOOLEAN(), autoincrement=False, nullable=False))
    op.add_column('user_settings', sa.Column('show_thumbnails', sa.BOOLEAN(), autoincrement=False, nullable=False))
    op.drop_column('user_settings', 'value')
    op.drop_column('user_settings', 'key')
    # ### end Alembic commands ###
