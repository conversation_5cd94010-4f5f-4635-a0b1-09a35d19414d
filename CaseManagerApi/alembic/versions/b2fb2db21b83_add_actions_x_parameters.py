"""add actions_x_parameters

Revision ID: b2fb2db21b83
Revises: 02a69882531f
Create Date: 2025-08-03 16:39:26.699274

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b2fb2db21b83'
down_revision: Union[str, None] = '02a69882531f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('parameters',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False),
    sa.Column('label', sa.String(length=100), nullable=False),
    sa.Column('description', sa.String(length=200), nullable=False),
    sa.Column('input', sa.<PERSON>an(), nullable=False),
    sa.Column('default_value', sa.String(length=200), nullable=True),
    sa.Column('required', sa.Boolean(), nullable=False),
    sa.Column('min_length', sa.Integer(), nullable=True),
    sa.Column('max_length', sa.Integer(), nullable=True),
    sa.Column('type_id', sa.UUID(), nullable=False),
    sa.ForeignKeyConstraint(['type_id'], ['action_parameter_types.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_parameters_id'), 'parameters', ['id'], unique=False)
    op.create_table('actions_x_parameters',
    sa.Column('action_id', sa.UUID(), nullable=False),
    sa.Column('parameter_id', sa.UUID(), nullable=False),
    sa.ForeignKeyConstraint(['action_id'], ['actions.id'], ),
    sa.ForeignKeyConstraint(['parameter_id'], ['parameters.id'], ),
    sa.PrimaryKeyConstraint('action_id', 'parameter_id')
    )
    op.drop_index(op.f('ix_action_parameters_id'), table_name='action_parameters')
    op.drop_constraint(op.f('action_instance_parameter_bindings_action_parameter_id_fkey'), 'action_instance_parameter_bindings', type_='foreignkey')
    op.drop_table('action_parameters')
    op.create_foreign_key('fx_parameters_action_parameter_id', 'action_instance_parameter_bindings', 'parameters', ['action_parameter_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('fx_parameters_action_parameter_id', 'action_instance_parameter_bindings', type_='foreignkey')
    op.create_table('action_parameters',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('name', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('description', sa.VARCHAR(length=200), autoincrement=False, nullable=False),
    sa.Column('input', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('default_value', sa.VARCHAR(length=200), autoincrement=False, nullable=True),
    sa.Column('action_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('type_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('label', sa.VARCHAR(length=100), autoincrement=False, nullable=False),
    sa.Column('required', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('min_length', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('max_length', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['action_id'], ['actions.id'], name=op.f('action_parameters_action_id_fkey')),
    sa.ForeignKeyConstraint(['type_id'], ['action_parameter_types.id'], name=op.f('fk_action_parameters_type_id')),
    sa.PrimaryKeyConstraint('id', name=op.f('action_parameters_pkey'))
    )
    op.create_foreign_key(op.f('action_instance_parameter_bindings_action_parameter_id_fkey'), 'action_instance_parameter_bindings', 'action_parameters', ['action_parameter_id'], ['id'])
    op.create_index(op.f('ix_action_parameters_id'), 'action_parameters', ['id'], unique=False)
    op.drop_table('actions_x_parameters')
    op.drop_index(op.f('ix_parameters_id'), table_name='parameters')
    op.drop_table('parameters')
    # ### end Alembic commands ###
