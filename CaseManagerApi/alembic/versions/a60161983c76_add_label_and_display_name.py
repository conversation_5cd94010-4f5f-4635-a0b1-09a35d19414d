"""add label and display name

Revision ID: a60161983c76
Revises: 44ce23a21328
Create Date: 2025-07-01 20:46:23.296927

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a60161983c76'
down_revision: Union[str, None] = '44ce23a21328'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('action_parameter_types', sa.Column('unique_name', sa.String(length=50), nullable=False))
    op.add_column('action_parameter_types', sa.Column('display_name', sa.String(length=100), nullable=False))
    op.drop_column('action_parameter_types', 'name')
    op.add_column('action_parameters', sa.Column('label', sa.String(length=100), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('action_parameters', 'label')
    op.add_column('action_parameter_types', sa.Column('name', sa.VARCHAR(length=50), autoincrement=False, nullable=False))
    op.drop_column('action_parameter_types', 'display_name')
    op.drop_column('action_parameter_types', 'unique_name')
    # ### end Alembic commands ###
