"""add transactions emails actions

Revision ID: d214572d3153
Revises: 8b868cac5c81
Create Date: 2025-06-23 21:19:38.729803

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd214572d3153'
down_revision: Union[str, None] = '8b868cac5c81'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('action_statuses',
    sa.Column('id', sa.String(length=50), nullable=False),
    sa.Column('description', sa.String(length=200), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_action_statuses_id'), 'action_statuses', ['id'], unique=False)
    op.create_table('currencies',
    sa.Column('id', sa.String(length=10), nullable=False),
    sa.Column('description', sa.String(length=200), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_currencies_id'), 'currencies', ['id'], unique=False)
    op.create_table('transaction_statuses',
    sa.Column('id', sa.String(length=50), nullable=False),
    sa.Column('description', sa.String(length=200), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_transaction_statuses_id'), 'transaction_statuses', ['id'], unique=False)
    op.create_table('transaction_types',
    sa.Column('id', sa.String(length=50), nullable=False),
    sa.Column('description', sa.String(length=200), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_transaction_types_id'), 'transaction_types', ['id'], unique=False)
    op.create_table('actions',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('document_id', sa.String(length=50), nullable=False),
    sa.ForeignKeyConstraint(['document_id'], ['documents.id'], ),
    sa.ForeignKeyConstraint(['status'], ['action_statuses.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_actions_id'), 'actions', ['id'], unique=False)
    op.create_table('emails',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('sender', sa.String(length=200), nullable=False),
    sa.Column('recipient', sa.String(length=200), nullable=False),
    sa.Column('subject', sa.String(length=200), nullable=False),
    sa.Column('body', sa.String(length=500), nullable=False),
    sa.Column('document_id', sa.String(length=50), nullable=False),
    sa.Column('sent_date', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['document_id'], ['documents.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_emails_id'), 'emails', ['id'], unique=False)
    op.create_table('transactions',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('type_id', sa.String(length=50), nullable=False),
    sa.Column('status_id', sa.String(length=50), nullable=False),
    sa.Column('document_id', sa.String(length=50), nullable=False),
    sa.Column('currency_id', sa.String(length=10), nullable=False),
    sa.Column('amount', sa.DECIMAL(precision=10, scale=2), nullable=False),
    sa.Column('recipient', sa.String(length=200), nullable=False),
    sa.Column('send_date', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('comment', sa.String(length=500), nullable=True),
    sa.ForeignKeyConstraint(['currency_id'], ['currencies.id'], ),
    sa.ForeignKeyConstraint(['document_id'], ['documents.id'], ),
    sa.ForeignKeyConstraint(['status_id'], ['transaction_statuses.id'], ),
    sa.ForeignKeyConstraint(['type_id'], ['transaction_types.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_transactions_id'), 'transactions', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_transactions_id'), table_name='transactions')
    op.drop_table('transactions')
    op.drop_index(op.f('ix_emails_id'), table_name='emails')
    op.drop_table('emails')
    op.drop_index(op.f('ix_actions_id'), table_name='actions')
    op.drop_table('actions')
    op.drop_index(op.f('ix_transaction_types_id'), table_name='transaction_types')
    op.drop_table('transaction_types')
    op.drop_index(op.f('ix_transaction_statuses_id'), table_name='transaction_statuses')
    op.drop_table('transaction_statuses')
    op.drop_index(op.f('ix_currencies_id'), table_name='currencies')
    op.drop_table('currencies')
    op.drop_index(op.f('ix_action_statuses_id'), table_name='action_statuses')
    op.drop_table('action_statuses')
    # ### end Alembic commands ###
