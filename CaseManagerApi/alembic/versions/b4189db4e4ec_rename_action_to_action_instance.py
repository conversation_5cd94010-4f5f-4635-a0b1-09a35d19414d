"""rename action to action instance

Revision ID: b4189db4e4ec
Revises: d214572d3153
Create Date: 2025-06-26 20:03:33.231013

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'b4189db4e4ec'
down_revision: Union[str, None] = 'd214572d3153'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('action_instances',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('document_id', sa.String(length=50), nullable=False),
    sa.ForeignKeyConstraint(['document_id'], ['documents.id'], ),
    sa.ForeignKeyConstraint(['status'], ['action_statuses.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_action_instances_id'), 'action_instances', ['id'], unique=False)
    op.drop_index(op.f('ix_actions_id'), table_name='actions')
    op.drop_table('actions')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('actions',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('name', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('status', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('document_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['document_id'], ['documents.id'], name=op.f('actions_document_id_fkey')),
    sa.ForeignKeyConstraint(['status'], ['action_statuses.id'], name=op.f('actions_status_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('actions_pkey'))
    )
    op.create_index(op.f('ix_actions_id'), 'actions', ['id'], unique=False)
    op.drop_index(op.f('ix_action_instances_id'), table_name='action_instances')
    op.drop_table('action_instances')
    # ### end Alembic commands ###
