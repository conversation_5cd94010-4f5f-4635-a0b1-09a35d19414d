"""add action params and bindings

Revision ID: 8504b6a9619f
Revises: d7e8b035d8cd
Create Date: 2025-06-29 22:33:45.874323

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8504b6a9619f'
down_revision: Union[str, None] = 'd7e8b035d8cd'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('action_instance_parameter_bindings',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('value', sa.String(length=200), nullable=False),
    sa.Column('action_instance_id', sa.UUID(), nullable=False),
    sa.Column('action_parameter_id', sa.UUID(), nullable=False),
    sa.ForeignKeyConstraint(['action_instance_id'], ['action_instances.id'], ),
    sa.ForeignKeyConstraint(['action_parameter_id'], ['action_parameters.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_action_instance_parameter_bindings_id'), 'action_instance_parameter_bindings', ['id'], unique=False)
    op.add_column('action_instances', sa.Column('action_id', sa.UUID(), nullable=False))
    op.create_foreign_key('fk_action_instances_action_id', 'action_instances', 'actions', ['action_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # op.drop_constraint('fk_action_instances_action_id', 'action_instances', type_='foreignkey')
    op.drop_column('action_instances', 'action_id')
    op.drop_index(op.f('ix_action_instance_parameter_bindings_id'), table_name='action_instance_parameter_bindings')
    op.drop_table('action_instance_parameter_bindings')
    # ### end Alembic commands ###
