"""add debtors

Revision ID: 30c15176207a
Revises: b2fb2db21b83
Create Date: 2025-08-03 17:36:27.940735

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '30c15176207a'
down_revision: Union[str, None] = 'b2fb2db21b83'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('debtors',
    sa.Column('id', sa.String(length=100), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('email', sa.String(length=100), nullable=True),
    sa.Column('phone', sa.String(length=100), nullable=True),
    sa.Column('address', sa.String(length=100), nullable=True),
    sa.Column('description', sa.String(length=500), nullable=False, default=""),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('cases', sa.Column('debtor_id', sa.String(length=100), nullable=False))
    op.create_foreign_key('fx_cases_debtor_id', 'cases', 'debtors', ['debtor_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('fx_cases_debtor_id', 'cases', type_='foreignkey')
    op.drop_column('cases', 'debtor_id')
    op.drop_table('debtors')
    # ### end Alembic commands ###
