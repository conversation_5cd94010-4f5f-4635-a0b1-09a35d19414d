"""add order to actions and action_groups

Revision ID: b0c36197e0b2
Revises: 90f07857405d
Create Date: 2025-08-09 12:43:38.108456

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b0c36197e0b2'
down_revision: Union[str, None] = '90f07857405d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('action_groups', sa.Column('order', sa.Integer(), nullable=False))
    op.add_column('actions', sa.Column('order', sa.Integer(), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('actions', 'order')
    op.drop_column('action_groups', 'order')
    # ### end Alembic commands ###
