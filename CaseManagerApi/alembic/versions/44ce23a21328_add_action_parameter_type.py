"""add action parameter type

Revision ID: 44ce23a21328
Revises: 8504b6a9619f
Create Date: 2025-06-30 23:34:23.693091

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '44ce23a21328'
down_revision: Union[str, None] = '8504b6a9619f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('action_parameter_types',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False),
    sa.Column('regex', sa.String(length=200), nullable=True),
    sa.Column('description', sa.String(length=200), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_action_parameter_types_id'), 'action_parameter_types', ['id'], unique=False)
    op.add_column('action_parameters', sa.Column('type_id', sa.UUID(), nullable=False))
    op.create_foreign_key('fk_action_parameters_type_id', 'action_parameters', 'action_parameter_types', ['type_id'], ['id'])
    op.drop_column('action_parameters', 'type')
    op.drop_column('action_parameters', 'regex')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('action_parameters', sa.Column('regex', sa.VARCHAR(length=200), autoincrement=False, nullable=True))
    op.add_column('action_parameters', sa.Column('type', sa.VARCHAR(length=50), autoincrement=False, nullable=False))
    # op.drop_constraint('fk_action_parameters_type_id', 'action_parameters', type_='foreignkey')
    op.drop_column('action_parameters', 'type_id')
    op.drop_index(op.f('ix_action_parameter_types_id'), table_name='action_parameter_types')
    op.drop_table('action_parameter_types')
    # ### end Alembic commands ###
