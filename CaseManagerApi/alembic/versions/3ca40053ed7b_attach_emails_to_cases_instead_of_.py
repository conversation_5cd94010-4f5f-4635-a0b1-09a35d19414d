"""attach emails to cases instead of documents

Revision ID: 3ca40053ed7b
Revises: 30c15176207a
Create Date: 2025-08-06 10:38:36.314598

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3ca40053ed7b'
down_revision: Union[str, None] = '30c15176207a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('emails', sa.Column('case_id', sa.String(length=100), nullable=False))
    op.drop_constraint(op.f('emails_document_id_fkey'), 'emails', type_='foreignkey')
    op.create_foreign_key('fk_emails_case_id', 'emails', 'cases', ['case_id'], ['id'])
    op.drop_column('emails', 'document_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('emails', sa.Column('document_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False))
    op.drop_constraint('fk_emails_case_id', 'emails', type_='foreignkey')
    op.create_foreign_key(op.f('emails_document_id_fkey'), 'emails', 'documents', ['document_id'], ['id'])
    op.drop_column('emails', 'case_id')
    # ### end Alembic commands ###
