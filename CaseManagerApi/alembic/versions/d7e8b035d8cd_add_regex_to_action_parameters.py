"""add regex to action_parameters

Revision ID: d7e8b035d8cd
Revises: 17ae79fe9bc3
Create Date: 2025-06-27 11:48:34.397877

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd7e8b035d8cd'
down_revision: Union[str, None] = '17ae79fe9bc3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('action_parameters', sa.Column('regex', sa.String(length=200), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('action_parameters', 'regex')
    # ### end Alembic commands ###
