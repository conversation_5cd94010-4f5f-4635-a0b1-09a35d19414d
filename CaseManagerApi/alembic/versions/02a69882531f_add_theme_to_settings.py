"""add theme to settings

Revision ID: 02a69882531f
Revises: 991b878ccbf8
Create Date: 2025-08-02 20:06:08.316031

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '02a69882531f'
down_revision: Union[str, None] = '991b878ccbf8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_settings', sa.Column('theme', sa.String(length=50), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_settings', 'theme')
    # ### end Alembic commands ###
