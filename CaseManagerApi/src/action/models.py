from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Column, Table
from sqlalchemy.orm import mapped_column, relationship

from data.database.config import Base

class ActionGroup(Base):
    __tablename__ = "action_groups"

    id = mapped_column(UUID(as_uuid=True), primary_key=True, index=True, nullable=False)
    name = mapped_column(String(50), nullable=False)
    description = mapped_column(String(200), nullable=False)
    order = mapped_column(Integer, nullable=False, default=0)

    actions = relationship("Action", back_populates="group")

actions_x_parameters = Table(
    "actions_x_parameters",
    Base.metadata,
    Column(
        "action_id",
        UUID(as_uuid=True),
        ForeignKey("actions.id"),
        primary_key=True,
        nullable=False,
    ),
    Column(
        "parameter_id",
        UUID(as_uuid=True),
        ForeignKey("parameters.id"),
        primary_key=True,
        nullable=False,
    )
)

class ActionParameter(Base):
    __tablename__ = "parameters"

    id = mapped_column(UUID(as_uuid=True), primary_key=True, index=True, nullable=False)
    name = mapped_column(String(50), nullable=False)
    label = mapped_column(String(100), nullable=False)
    description = mapped_column(String(200), nullable=False)
    input = mapped_column(Boolean, nullable=False)
    default_value = mapped_column(String(200), nullable=True)
    required = mapped_column(Boolean, nullable=False)
    min_length = mapped_column(Integer, nullable=True)
    max_length = mapped_column(Integer, nullable=True)

    type_id = mapped_column(UUID(as_uuid=True), ForeignKey("action_parameter_types.id"), nullable=False)
    type = relationship("ActionParameterType")

    bindings = relationship("ActionInstanceParameterBinding", back_populates="parameter")
    actions = relationship("Action", back_populates="parameters", secondary=actions_x_parameters)

class ActionParameterType(Base):
    __tablename__ = "action_parameter_types"

    id = mapped_column(UUID(as_uuid=True), primary_key=True, index=True, nullable=False)
    unique_name = mapped_column(String(50), nullable=False)
    display_name = mapped_column(String(100), nullable=False)
    regex = mapped_column(String(200), nullable=True)
    description = mapped_column(String(200), nullable=False)

class Action(Base):
    __tablename__ = "actions"

    id = mapped_column(UUID(as_uuid=True), primary_key=True, index=True, nullable=False)
    name = mapped_column(String(50), nullable=False)
    description = mapped_column(String(200), nullable=False)
    order = mapped_column(Integer, nullable=False, default=0)

    group_id = mapped_column(UUID(as_uuid=True), ForeignKey("action_groups.id"), nullable=False)
    group = relationship("ActionGroup", back_populates="actions")
    parameters = relationship("ActionParameter", back_populates="actions", secondary=actions_x_parameters)

    instances = relationship("ActionInstance", back_populates="action")