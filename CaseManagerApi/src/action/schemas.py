from uuid import UUID
from pydantic import ConfigDict
from src.schemas import CamelModel

BaseModel = CamelModel

class ActionParameterTypeData(BaseModel):
    id: UUID
    display_name: str
    unique_name: str
    description: str
    regex: str | None

    model_config = ConfigDict(from_attributes=True)

class ActionParameterData(BaseModel):
    id: UUID
    name: str
    label: str
    description: str
    input: bool
    required: bool
    min_length: int | None
    max_length: int | None
    default_value: str | None
    type: ActionParameterTypeData

    model_config = ConfigDict(from_attributes=True)

class ActionGroupData(BaseModel):
    id: UUID
    name: str
    description: str

    model_config = ConfigDict(from_attributes=True)
class ActionData(BaseModel):
    id: UUID
    name: str
    description: str

    group: ActionGroupData
    parameters: list[ActionParameterData]

    model_config = ConfigDict(from_attributes=True)

