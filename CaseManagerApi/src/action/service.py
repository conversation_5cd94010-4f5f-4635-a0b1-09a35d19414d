import uuid

from fastapi import HTTPException, Depends
from sqlalchemy.orm import selectinload, Session
from starlette import status

from src.action.models import Action
from data.database.config import get_db


async def get_action_with_params(action_id: uuid.UUID, db: Session = Depends(get_db)) -> Action:
    action: Action | None = db.get(Action, action_id, options=(selectinload(Action.parameters),))
    if action is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Action does not exist",
        )
    return action