from fastapi import APIRouter, Depends
from sqlalchemy import select
from sqlalchemy.orm import Session, selectinload

from src.action.models import Action, ActionGroup
from src.action.schemas import ActionData
from src.auth.utils import get_current_active_user
from data.database.config import get_db

router = APIRouter(
    prefix="/api",
    tags=["action"],
    dependencies=[Depends(get_current_active_user)],
)

@router.get(
    path="/actions",
    summary="Get actions",
    description="Get actions by document ID",
    operation_id="getActions",
    response_model=list[ActionData],
)
async def get_actions(db: Session = Depends(get_db)) -> list[ActionData]:
    actions = (
        db.scalars(
            select(Action)
            .order_by(Action.group.order)
            .options(
                selectinload(Action.group),
                selectinload(Action.parameters),
            )
        ).all()
    )

    return [ActionData.model_validate(a, from_attributes=True) for a in actions]
