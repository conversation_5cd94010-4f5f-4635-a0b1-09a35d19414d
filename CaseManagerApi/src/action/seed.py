import random
from uuid import uuid4

from src.action.models import ActionParameter, Action, ActionGroup, ActionParameterType

SEEDED_ACTION_GROUPS = [
    ActionGroup(id=uuid4(), name="Group 1", description="Group 1 description"),
    ActionGroup(id=uuid4(), name="Group 2", description="Group 2 description"),
    ActionGroup(id=uuid4(), name="Group 3", description="Group 3 description"),
]

def generate_seeded_actions(n: int = 30):
    """Return *n* pseudo-random actions."""
    return  [
        Action(
            id=uuid4(),
            name= f"Action {i + 1}",
            description= f"Description for action {i + 1}",
            group_id= random.choice(SEEDED_ACTION_GROUPS).id,
        ) for i in range(n)
    ]

SEEDED_ACTIONS = generate_seeded_actions()
action_ids = [action.id for action in SEEDED_ACTIONS]

SEEDED_PARAMETER_TYPES = [
    ActionParameterType(id=uuid4(), unique_name="string", display_name="String", description="String parameter", regex="^[a-zA-Z0-9]+$"),
    ActionParameterType(id=uuid4(), unique_name="number", display_name="Number", description="Number parameter", regex="^[0-9]+$"),
    ActionParameterType(id=uuid4(), unique_name="boolean", display_name="Boolean", description="Boolean parameter", regex="^(true|false)$"),
    ActionParameterType(id=uuid4(), unique_name="date", display_name="Date", description="Date parameter", regex="^[0-9]{2}/[0-9]{2}/[0-9]{4}$"),
    ActionParameterType(id=uuid4(), unique_name="email", display_name="Email", description="Email parameter", regex="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"),
    ActionParameterType(id=uuid4(), unique_name="phone", display_name="Phone", description="Phone parameter", regex="^[0-9]{3}-[0-9]{3}-[0-9]{4}$"),
    ActionParameterType(id=uuid4(), unique_name="raw_text", display_name="Raw text", description="Raw text parameter"),
]

def generate_action_params(n: int = 100):
    parameter_types = [parameter_type.id for parameter_type in SEEDED_PARAMETER_TYPES]

    possible_default_values = [
        None,
        *(["default"] * 20),
        "%document_file_path%",
        "%document_file_name%",
        "%user_name%",
        "%creation_date#%Y-%m-%d %H:%M:%S%",
        "%creation_date#%Y-%m-%d%",
        "%creation_date#%H:%M:%S%",
        "%case_id%",
        "%action_type%",
    ]
    return [
        ActionParameter(
            id=uuid4(),
            name=f"PARAM{i + 1}",
            label=f"Param {i + 1} Label",
            description=f"Description for param {i + 1}",
            input=random.choice([True, True, False]),
            required=random.choice([True, True, False]),
            min_length=1,
            max_length=100,
            default_value=random.choice(possible_default_values),
            type_id=random.choice(parameter_types))
        for i in range(n)
    ]

SEEDED_ACTION_PARAMS = generate_action_params()

def generate_actions_x_parameters(n: int = 70):
    parameter_ids = [p.id for p in SEEDED_ACTION_PARAMS]
    all_pairs = [(a, p) for a in action_ids for p in parameter_ids]

    if n > len(all_pairs):
        raise ValueError("n is larger than the number of unique pairs possible")
    pairs = set(random.sample(all_pairs, n))

    return [{"action_id": a, "parameter_id": p} for a, p in pairs]

SEEDED_ACTIONS_X_PARAMETERS = generate_actions_x_parameters()