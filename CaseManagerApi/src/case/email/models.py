from sqlalchemy import UUI<PERSON>, String, ForeignKey, DateTime, func
from sqlalchemy.orm import mapped_column, relationship

from data.database.config import Base


class Email(Base):
    __tablename__ = "emails"

    id = mapped_column(UUID(as_uuid=True), primary_key=True, index=True, nullable=False)
    sender = mapped_column(String(200), nullable=False)
    recipient = mapped_column(String(200), nullable=False)
    subject = mapped_column(String(200), nullable=False)
    body = mapped_column(String(500), nullable=False)
    case_id = mapped_column(String(100), ForeignKey("cases.id"), nullable=False)
    sent_date = mapped_column(DateTime(timezone=True), server_default=func.now(), nullable=False)

    case = relationship("Case", back_populates="emails")
