import random
from datetime import datetime, timedelta
from uuid import uuid4

from src.case.email.models import Email
from src.case.seed import SEEDED_CASES


def generate_seeded_emails(n: int = 1000):
    """Return *n* pseudo-random emails."""
    case_ids = [c.id for c in SEEDED_CASES]
    emails = []
    for i in range(n):
        case_id = random.choice(case_ids)
        new_email = Email(
            id=uuid4(),
            case_id=case_id,
            sender=f"sender{i + 1}@example.com",
            recipient=f"recipient{i + 1}@example.com",
            subject=f"Seed email {i + 1} for {case_id}",
            body=f"This is the body of email {i + 1} for {case_id}.",
            sent_date=datetime.now() - timedelta(days=random.randint(0, 60)),
        )
        emails.append(new_email)

    return emails


SEEDED_EMAILS = generate_seeded_emails()
