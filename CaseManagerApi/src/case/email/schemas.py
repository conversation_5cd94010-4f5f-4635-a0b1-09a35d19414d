import uuid
from datetime import datetime

from pydantic import Field,  ConfigDict, EmailStr, constr

from src.case.schemas import BaseModel
from src.schemas import CamelModel

class SearchEmailsInput(BaseModel):
    document_id: str = Field(None, description="Document ID associated with the email")

class EmailData(BaseModel):
    id: uuid.UUID
    subject: str
    body: str
    sender: str
    recipient: str
    sent_date: datetime
    case_id: str

    model_config = ConfigDict(from_attributes=True)

class CreateEmailInput(BaseModel):
    recipient:  EmailStr
    subject:    constr(min_length=1, max_length=200)
    body:       constr(min_length=1, max_length=500)
    document_id:str
