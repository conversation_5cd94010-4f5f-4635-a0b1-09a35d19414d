import os
import uuid
from datetime import datetime
from email.message import EmailMessage
from email.policy import SM<PERSON>

from fastapi import Depends, APIRouter
from sqlalchemy import select
from sqlalchemy.orm import Session

from src.auth.utils import get_current_active_user
from data.database.config import get_db
from src.case.email.config import emails_root, sender_email
from src.case.email.models import Email
from src.case.email.schemas import SearchEmailsInput, EmailData, CreateEmailInput
from src.case.service import get_case_id

router = APIRouter(
    prefix="/api",
    tags=["email"],
    dependencies=[Depends(get_current_active_user)],
)

@router.get(
    path="/emails",
    summary="Get emails",
    description="Get emails related to a case",
    operation_id="getEmails",
    response_model=list[EmailData],
)
async def get_emails(case_id: str = Depends(get_case_id), db: Session = Depends(get_db)) -> list[EmailData]:
    emails = db.scalars(select(Email).where(Email.case_id == case_id)).all()
    return [EmailData.model_validate(email) for email in emails]

@router.post(
    path="/emails",
    summary="Create an email",
    description="Create an email related to a document",
    operation_id="createEmail",
    response_model=EmailData,
)
async def create_email(params: CreateEmailInput, db: Session = Depends(get_db)) -> EmailData:
    db_email = Email(**params.model_dump(), sender=sender_email, sent_date=datetime.now(), id=uuid.uuid4())
    db.add(db_email)

    msg = EmailMessage(policy=SMTP)
    msg["From"] = db_email.sender
    msg["To"] = db_email.recipient
    msg["Subject"] = db_email.subject
    msg.set_content(db_email.body)

    os.makedirs(emails_root, exist_ok=True)
    subject = db_email.subject.replace(" ", "_")
    file_name = f"{subject}_{db_email.sent_date.strftime('%Y-%m-%d_%H-%M-%S')}.imf"
    imf_path = os.path.join(emails_root, file_name)
    with open(imf_path, "wb") as f:
        f.write(msg.as_bytes())

    db.commit()
    db.refresh(db_email)

    return EmailData.model_validate(db_email)