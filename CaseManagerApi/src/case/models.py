from sqlalchemy import DateTime, Foreign<PERSON><PERSON>, String, func
from sqlalchemy.orm import mapped_column, relationship

from data.database.config import Base

class Case(Base):
    __tablename__ = "cases"

    id = mapped_column(String(100), primary_key=True, index=True, nullable=False)
    display_name = mapped_column(String(100), nullable=False)
    description = mapped_column(String(200), nullable=False)
    created_at = mapped_column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = mapped_column(DateTime(timezone=True), server_default=func.now(), nullable=False)

    debtor_id = mapped_column(String(100), ForeignKey("debtors.id"), nullable=False)

    emails = relationship("Email", back_populates="case")