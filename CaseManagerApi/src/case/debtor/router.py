from fastapi import Depends, APIRouter, HTTPException
from sqlalchemy import select
from starlette import status

from src.auth.utils import get_current_active_user
from src.case.debtor.models import Debtor
from src.case.debtor.schemas import DebtorData
from src.case.models import Case
from src.case.service import get_case_id
from data.database.config import get_db

router = APIRouter(
    prefix="/api",
    tags=["debtor"],
    dependencies=[Depends(get_current_active_user)],
)

@router.get("/debtor",
            summary="Get debtor",
            description="Get debtor",
            operation_id="getDebtor",
            response_model=DebtorData)
def get_debtor(case_id=Depends(get_case_id) , db=Depends(get_db)) -> DebtorData:
    debtor = db.scalar(select(Debtor).where(Debtor.cases.any(Case.id == case_id)))
    if debtor is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Debtor not found")

    return DebtorData.model_validate(debtor)