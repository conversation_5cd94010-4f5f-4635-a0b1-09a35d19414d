from sqlalchemy import String, DateTime, func
from sqlalchemy.orm import mapped_column, relationship, Mapped

from src.case.models import Case
from data.database.config import Base


class Debtor(Base):
    __tablename__ = "debtors"

    id = mapped_column(String(100), primary_key=True,)
    name = mapped_column(String(100), nullable=False)
    email = mapped_column(String(100), nullable=True)
    phone = mapped_column(String(100), nullable=True)
    address = mapped_column(String(100), nullable=True)
    description = mapped_column(String(500), nullable=False)
    created_at = mapped_column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = mapped_column(DateTime(timezone=True), server_default=func.now(), nullable=False)

    cases: Mapped[list[Case]] = relationship(lambda: Case)

