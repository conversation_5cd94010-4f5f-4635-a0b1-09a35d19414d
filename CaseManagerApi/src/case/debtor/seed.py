from __future__ import annotations

import re
from datetime import datetime
from zoneinfo import ZoneInfo

from faker import Faker
from unidecode import unidecode  # pip install Unidecode

from polyfactory.factories.sqlalchemy_factory import SQLAlchemyFactory
from polyfactory.decorators import post_generated

from src.case.debtor.models import Debtor

HUN_TZ = ZoneInfo("Europe/Budapest")

class DebtorFactory(SQLAlchemyFactory[Debtor]):
    """
    Realistic Debtor rows:
      - hu_HU names / addresses / phones
      - email derived from name
      - timezone-aware timestamps
    """
    __model__ = Debtor
    __set_relationships__ = False
    __faker__ = Faker("hu_HU")
    __random_seed__ = 2025  # make output reproducible

    # --- core string columns ------------------------------------------------------
    @classmethod
    def name(cls) -> str:
        return cls.__faker__.name()

    @classmethod
    def phone(cls) -> str:
        # +36 20|30|70 123 4567
        return f"+36 {cls.__random__.choice([20, 30, 70])} " \
               f"{cls.__random__.randint(100, 999)} " \
               f"{cls.__random__.randint(1000, 9999)}"

    @classmethod
    def address(cls) -> str:
        return cls.__faker__.address()

    @classmethod
    def description(cls) -> str:
        return cls.__faker__.paragraph(nb_sentences=2)

    # --- timestamps ---------------------------------------------------------------
    @classmethod
    def created_at(cls) -> datetime:
        return cls.__faker__.date_time_between(
            start_date="-18M", end_date="-1d", tzinfo=HUN_TZ
        )

    @post_generated
    @classmethod
    def updated_at(cls, created_at: datetime) -> datetime:
        # ensure updated_at >= created_at
        return cls.__faker__.date_time_between(
            start_date=created_at, end_date="now", tzinfo=HUN_TZ
        )

    # --- derive email from name ---------------------------------------------------
    @staticmethod
    def _slug(text: str) -> str:
        s = unidecode(text).lower()
        return re.sub(r"[^a-z0-9]+", ".", s).strip(".")

    @post_generated
    @classmethod
    def email(cls, name: str) -> str:
        return f"{cls._slug(name)}@{cls.__faker__.free_email_domain()}"

def generate_debtors(n: int = 40) -> list[Debtor]:
    return [DebtorFactory.build(id=f"debtor_{i}") for i in range(n)]


SEEDED_DEBTORS = generate_debtors()
