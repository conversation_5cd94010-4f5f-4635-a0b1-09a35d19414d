from datetime import datetime, timezone
import random

from src.case.debtor.seed import SEEDED_DEBTORS
from src.case.models import Case


def generate_cases(n: int = 100) -> list[Case]:
    debtor_ids = [d.id for d in SEEDED_DEBTORS]
    return [
        Case(
            id=f"case-{i+1}",
            display_name=f"Case {i+1}",
            description=f"Description for case {i+1}",
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
            debtor_id=random.choice(debtor_ids),
        ) for i in range(n)
    ]

SEEDED_CASES = generate_cases()