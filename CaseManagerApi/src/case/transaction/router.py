from fastapi import APIRouter, Depends

from src.auth.utils import get_current_active_user
from sqlalchemy import select
from sqlalchemy.orm import Session

from src.case.service import get_case_id
from data.database.config import get_db
from src.case.transaction.models import Transaction
from src.case.transaction.schemas import TransactionData

router = APIRouter(
    prefix="/api",
    tags=["transaction"],
    dependencies=[Depends(get_current_active_user)],
)

@router.get(
    path="/transactions",
    summary="Get transactions",
    description="Get transactions by the provided filters",
    operation_id="getTransactions",
    response_model=list[TransactionData],
)
async def get_transactions(case_id: str = Depends(get_case_id), db: Session = Depends(get_db)) -> list[TransactionData]:
    query = select(Transaction).where(Transaction.case_id == case_id)
    transactions = db.scalars(query).all()
    return [TransactionData.model_validate(transaction) for transaction in transactions]
