from sqlalchemy import DE<PERSON><PERSON><PERSON>, UUID, DateTime, ForeignKey, String, func
from sqlalchemy.orm import mapped_column

from data.database.config import Base


class TransactionType(Base):
    __tablename__ = "transaction_types"

    id = mapped_column(String(50), primary_key=True, index=True, nullable=False)
    description = mapped_column(String(200), nullable=False)

class TransactionStatus(Base):
    __tablename__ = "transaction_statuses"

    id = mapped_column(String(50), primary_key=True, index=True, nullable=False)
    description = mapped_column(String(200), nullable=False)

class Currency(Base):
    __tablename__ = "currencies"

    id = mapped_column(String(10), primary_key=True, index=True, nullable=False)
    description = mapped_column(String(200), nullable=False)

class Transaction(Base):
    __tablename__ = "transactions"

    id = mapped_column(UUID(as_uuid=True), primary_key=True, index=True, nullable=False)

    type_id = mapped_column(
        String(50),
        ForeignKey(TransactionType.__tablename__ + ".id"),
        nullable=False)
    status_id = mapped_column(
        String(50),
        ForeignKey(TransactionStatus.__tablename__ + ".id"),
        nullable=False)
    case_id = mapped_column(
        String(100),
        ForeignKey("cases.id"),
        nullable=False)
    currency_id = mapped_column(
        String(10),
        ForeignKey(Currency.__tablename__ + ".id"),
        nullable=False)

    amount = mapped_column(DECIMAL(precision=10, scale=2), nullable=False)
    recipient = mapped_column(String(200), nullable=False)
    send_date = mapped_column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    comment = mapped_column(String(500), nullable=True)