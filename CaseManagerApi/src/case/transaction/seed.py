import importlib
import random
from datetime import datetime, timedelta
from uuid import uuid4

from src.case.transaction.models import TransactionType, TransactionStatus, Currency, Transaction

pycountry = importlib.import_module("pycountry")

SEEDED_TRANSACTION_TYPES = [
    TransactionType(id="payment", description="Payment transaction"),
    TransactionType(id="refund", description="Refund transaction"),
    TransactionType(id="withdrawal", description="Withdrawal transaction"),
    TransactionType(id="deposit", description="Deposit transaction"),
    TransactionType(id="transfer", description="Transfer transaction"),
    TransactionType(id="fee", description="Fee adjustment"),
    TransactionType(id="adjustment", description="Manual adjustment"),
]

SEEDED_TRANSACTION_STATUSES = [
    TransactionStatus(id="pending", description="Pending transaction"),
    TransactionStatus(id="completed", description="Completed transaction"),
    TransactionStatus(id="failed", description="Failed transaction"),
    TransactionStatus(id="cancelled", description="Cancelled transaction"),
    TransactionStatus(id="in_progress", description="In progress transaction"),
]

SEEDED_CURRENCIES = [
    Currency(id=cur.alpha_3, description=cur.name)
    for cur in pycountry.currencies
    if not getattr(cur, "withdrawal_date", None)
]

def generate_seeded_transactions(n: int = 1000):
    """
    Return *n* pseudo-random transactions covering a range of
    types, statuses, and currencies.
    """
    case_ids = [f"case-{i+1}" for i in range(100)]

    transactions = []
    now = datetime.now()

    for i in range(n):
        tx_type = random.choice(SEEDED_TRANSACTION_TYPES).id
        new_transaction = Transaction(
            id=uuid4(),
            type_id=tx_type,
            status_id=random.choice(SEEDED_TRANSACTION_STATUSES).id,
            case_id=random.choice(case_ids),
            currency_id=random.choice(SEEDED_CURRENCIES).id,
            amount=round(random.uniform(1, 10_000), 2),
            recipient=f"Recipient {i+1}",
            send_date=now
            - timedelta(
                days=random.randint(0, 60),
                hours=random.randint(0, 23),
                minutes=random.randint(0, 59),
            ),
            comment=f"Auto-generated {tx_type} #{i+1}",
        )
        transactions.append(new_transaction)

    return transactions

SEEDED_TRANSACTIONS = generate_seeded_transactions()