from datetime import datetime
from uuid import UUID

from pydantic import Field, ConfigDict

from src.case.schemas import BaseModel
from src.schemas import CamelModel

class TransactionData(BaseModel):
    id: UUID
    type_id: str
    status_id: str
    case_id: str
    amount: float
    currency_id: str
    recipient: str
    send_date: datetime
    comment: str | None

    model_config = ConfigDict(from_attributes=True)