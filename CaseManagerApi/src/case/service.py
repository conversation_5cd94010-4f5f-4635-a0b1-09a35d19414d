from fastapi import Depends, HTTPException
from sqlalchemy import select
from sqlalchemy.orm import Session
from starlette import status

from src.case.schemas import IdentifyCaseInput
from data.database.config import get_db
from src.document.models import Document


async def get_case_id(params: IdentifyCaseInput = Depends(), db: Session = Depends(get_db)) -> str:
    if params.document_id:
        if params.case_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot specify both document_id and case_id",
            )
        case_for_doc = db.scalar(select(Document.case_id).where(Document.id == params.document_id))
        if case_for_doc is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document does not exist, or there is no case associated with it.",
            )
        return case_for_doc
    elif params.case_id is None:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Either document_id or case_id must be specified",
        )
    return params.case_id