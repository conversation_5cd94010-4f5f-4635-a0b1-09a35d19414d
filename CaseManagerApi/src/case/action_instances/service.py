import re
import uuid

from fastapi import Depends, HTTPException
from sqlalchemy.orm import Session
from starlette import status

from src.action.models import ActionParameter
from src.action import service as action_service
from src.case.action_instances.error import UnresolvableValueError
from src.case.action_instances.models import ActionInstance, ActionStatusName, ActionInstanceParameterBinding
from src.case.action_instances.schemas import CreateActionInstanceInput, ActionInstanceParameterBindingData
from data.database.config import get_db

def resolve_value(value: str, action_instance: ActionInstance) -> str | UnresolvableValueError:
    should_be_resolved = value.startswith("%") and value.endswith("%")
    if not should_be_resolved:
        return value

    value_lower = value[1:-1].lower()
    if value_lower == "document_file_path": return action_instance.document.path
    if value_lower == "document_file_name": return action_instance.document.name
    if value_lower == "user_name":          return "admin" #TODO: get current user name
    if value_lower == "case_id":            return action_instance.document.case_id
    if value_lower == "action_type":        return action_instance.action.name

    tokens = value[1:-1].split("#", maxsplit=1)
    if tokens[0] == "creation_date":
        if len(tokens) == 1:
            tokens.append("%Y-%m-%d %H:%M:%S")

        # format: https://chatgpt.com/share/68653144-7da0-800d-83d0-7b0f6b0497c3
        return action_instance.created_at.strftime(tokens[1])

    return UnresolvableValueError()

async def create_action_instance_in_db(params: CreateActionInstanceInput, db: Session = Depends(get_db)) -> ActionInstance:
    action_instance = ActionInstance(
        id=uuid.uuid4(),
        action_id=params.action_id,
        status=ActionStatusName.pending,
        document_id=params.document_id,
    )
    db.add(action_instance)
    db.commit()
    db.refresh(action_instance)
    return action_instance


async def create_valid_parameter_bindings(
        action_instance: ActionInstance,
        input_bindings: list[ActionInstanceParameterBindingData],
        db: Session = Depends(get_db),
) -> list[ActionInstanceParameterBinding]:
    action = await action_service.get_action_with_params(action_instance.action_id, db)
    input_bindings_map = {binding.action_parameter_id: binding.value for binding in input_bindings}
    result_bindings = [
        create_binding(action_instance, input_bindings_map, param)
        for param in action.parameters
    ]
    params_map = {param.id: param for param in action.parameters}
    for binding in input_bindings:
        param = params_map[binding.action_parameter_id]
        if param.type.regex and not re.match(param.type.regex, binding.value):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Value '{binding.value}' does not match regex '{param.type.regex}' for parameter '{param.name}'",
            )

    return result_bindings


def create_binding(action_instance: ActionInstance, input_bindings_map: dict[uuid.UUID, str], param: ActionParameter) -> ActionInstanceParameterBinding:
    if param.input:
        value = input_bindings_map.get(param.id)
        if value is None and param.required:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Missing value for parameter '{param.label}'",
            )
        if value is None:
            value = param.default_value
    else:
        value = resolve_value(param.default_value, action_instance)
        if value is UnresolvableValueError and param.required:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cannot resolve default value '{param.default_value}' for parameter '{param.name}'",
            )
    return ActionInstanceParameterBinding(
        id=uuid.uuid4(),
        value=value,
        action_instance_id=action_instance.id,
        action_parameter_id=param.id,)
