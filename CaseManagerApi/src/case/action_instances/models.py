from sqlalchemy import UUID, DateTime, ForeignKey, String, func, Enum
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import mapped_column, relationship

from data.database.config import Base

class ActionStatus(Base):
    __tablename__ = "action_statuses"

    id = mapped_column(String(50), primary_key=True, index=True, nullable=False)
    description = mapped_column(String(200), nullable=False)

class ActionStatusName(Enum):
    pending = "pending"
    in_progress = "in_progress"
    completed = "completed"
    cancelled = "cancelled"

class ActionInstance(Base):
    __tablename__ = "action_instances"

    id = mapped_column(UUID(as_uuid=True), primary_key=True, index=True, nullable=False)
    status = mapped_column(String(50), ForeignKey(ActionStatus.__tablename__ + ".id"), nullable=False)
    updated_at = mapped_column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    created_at = mapped_column(DateTime(timezone=True), server_default=func.now(), nullable=False)

    document_id = mapped_column(String(50), ForeignKey("documents.id"), nullable=False)
    document = relationship("Document", back_populates="actions")

    action_id = mapped_column(UUID(as_uuid=True), ForeignKey("actions.id"), nullable=False)
    action = relationship("Action", back_populates="instances")

    parameter_bindings = relationship("ActionInstanceParameterBinding", back_populates="action_instance")

    @hybrid_property
    def case_id(self) -> str:
        return self.document.case_id

class ActionInstanceParameterBinding(Base):
    __tablename__ = "action_instance_parameter_bindings"

    id = mapped_column(UUID(as_uuid=True), primary_key=True, index=True, nullable=False)
    value = mapped_column(String(500), nullable=True)

    action_instance_id = mapped_column(UUID(as_uuid=True), ForeignKey("action_instances.id"), nullable=False)
    action_instance = relationship("ActionInstance", back_populates="parameter_bindings")

    action_parameter_id = mapped_column(UUID(as_uuid=True), ForeignKey("parameters.id"), nullable=False)
    parameter = relationship("ActionParameter", back_populates="bindings")

    def __repr__(self):
        return f"( value={self.value}, id={self.id}, action_instance_id={self.action_instance_id}, action_parameter_id={self.action_parameter_id})"
