import json
import os
import uuid

from fastapi import APIRouter, Depends

from src.action.models import Action
from src.case.action_instances import service
from src.case.action_instances.config import ACTION_INSTANCES_JSON_ROOT
from src.auth.utils import get_current_active_user
from sqlalchemy import select
from sqlalchemy.orm import Session, selectinload

from src.case.action_instances.schemas import ActionInstanceData, CreateActionInstanceInput, \
    CreateActionInstanceAndGenerateJsonResponse
from src.case.service import get_case_id
from data.database.config import get_db
from src.case.action_instances.models import ActionInstance, ActionInstanceParameterBinding
from src.document.models import Document

router = APIRouter(
    prefix="/api",
    tags=["action_instance"],
    dependencies=[Depends(get_current_active_user)],
)

@router.get(
    path="/action_instances",
    summary="Get action instances",
    description="Get action instances by document ID",
    operation_id="getActionInstances",
    response_model=list[ActionInstanceData],
)
async def get_action_instances(case_id: str = Depends(get_case_id), db: Session = Depends(get_db)) -> list[ActionInstanceData]:
    query = (
        select(ActionInstance)
        .join(ActionInstance.document)
        .where(Document.case_id == case_id)
        .options(
            selectinload(
                ActionInstance.parameter_bindings,
                ActionInstanceParameterBinding.parameter),
            selectinload(ActionInstance.action).selectinload(Action.parameters),
        )
    )
    actions = db.scalars(query).all()
    return [ActionInstanceData.model_validate(action) for action in actions]

@router.post(
    path="/action_instances",
    summary="Create action instance",
    description="Create action instance",
    operation_id="createActionInstance",
    response_model=ActionInstanceData,
)
async def create_action_instance(create_request: CreateActionInstanceInput, db: Session = Depends(get_db)) -> ActionInstanceData:
    new_action_instance = await service.create_action_instance_in_db(create_request, db)
    new_param_bindings = await service.create_valid_parameter_bindings(new_action_instance, create_request.parameters, db)

    db.add_all(new_param_bindings)
    db.commit()
    db.refresh(new_action_instance)

    return ActionInstanceData.model_validate(new_action_instance)

@router.post(
    path="/action_instances/{action_instance_id}/generate_json",
    summary="Generate JSON for action instance",
    description="Generate JSON for action instance",
    operation_id="generateJsonForActionInstance",
    response_model=dict,
)
async def generate_json_for_action_instance(action_instance_id: uuid.UUID, db: Session = Depends(get_db)) -> dict:
    action_instance_with_params = db.scalars(
        select(ActionInstance)
        .where(ActionInstance.id == action_instance_id)
        .options(
            selectinload(ActionInstance.parameter_bindings),
            selectinload(ActionInstance.document),
            selectinload(ActionInstance.action),
        )
    ).one()

    json_data = {}
    for binding in action_instance_with_params.parameter_bindings:
        json_data[binding.parameter.name] = binding.value

    os.makedirs(ACTION_INSTANCES_JSON_ROOT, exist_ok=True)
    with open(os.path.join(ACTION_INSTANCES_JSON_ROOT, f"{action_instance_id}.json"), "w") as f:
        json.dump(json_data, f, indent=2)
        f.write("\n")

    return json_data

@router.post(
    path="/action_instances/create_and_generate_json",
    summary="Create action instance and generate JSON",
    description="Create action instance and generate JSON",
    operation_id="createActionInstanceAndGenerateJson",
    response_model=CreateActionInstanceAndGenerateJsonResponse,
)
async def create_action_instance_and_generate_json(params: CreateActionInstanceInput, db: Session = Depends(get_db)) -> CreateActionInstanceAndGenerateJsonResponse:
    action_instance_data = await create_action_instance(params, db)
    json_data = await generate_json_for_action_instance(action_instance_data.id, db)

    return CreateActionInstanceAndGenerateJsonResponse(
        action_instance=action_instance_data,
        json_data=json_data,
    )
