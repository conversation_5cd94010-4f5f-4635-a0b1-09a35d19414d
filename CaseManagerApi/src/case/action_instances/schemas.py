import uuid
from datetime import datetime

from pydantic import ConfigDict, Field

from src.action.schemas import ActionData
from src.case.schemas import BaseModel

class SearchActionsInput(BaseModel):
    case_id: str | None = Field(None, description="Case ID associated with the action_instances")
    document_id: str | None = Field(None, description="Document ID associated with the action_instances")

class ActionInstanceParameterBindingData(BaseModel):
    action_parameter_id: uuid.UUID
    value: str

    model_config = ConfigDict(from_attributes=True)

class ActionInstanceData(BaseModel):
    id: uuid.UUID
    status: str
    document_id: str
    case_id: str
    updated_at: datetime
    created_at: datetime
    action: ActionData
    parameter_bindings: list[ActionInstanceParameterBindingData]

    model_config = ConfigDict(from_attributes=True)

class CreateActionInstanceInput(BaseModel):
    action_id: uuid.UUID
    document_id: str
    name: str
    parameters: list[ActionInstanceParameterBindingData]

class CreateActionInstanceAndGenerateJsonResponse(BaseModel):
    action_instance: ActionInstanceData
    json_data: dict