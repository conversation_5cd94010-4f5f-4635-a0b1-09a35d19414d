import random
from uuid import uuid4

from src.case.action_instances.models import ActionStatus, ActionInstance, ActionInstanceParameterBinding, ActionStatusName
from src.action.seed import SEEDED_ACTIONS, SEEDED_ACTION_PARAMS

SEEDED_ACTION_STATUSES = [
    ActionStatus(id=ActionStatusName.pending, description="Pending action_instances"),
    ActionStatus(id=ActionStatusName.in_progress, description="Action in progress"),
    ActionStatus(id=ActionStatusName.completed, description="Completed action_instances"),
    ActionStatus(id=ActionStatusName.cancelled, description="Cancelled action_instances"),
]

def generate_seeded_action_instances(n: int = 1000):
    """Return *n* pseudo-random actions."""
    doc_ids = [f"DOC-{i:04d}" for i in range(1, 41)]

    action_ids = [action.id for action in SEEDED_ACTIONS]

    return [
        ActionInstance(
            id=uuid4(),
            action_id=random.choice(action_ids),
            status=random.choice(SEEDED_ACTION_STATUSES).id,
            document_id=random.choice(doc_ids),
        ) for i in range(n)
    ]


SEEDED_ACTION_INSTANCES = generate_seeded_action_instances()

def generate_action_parameter_bindings(n: int = 5000):
    """Return *n* pseudo-random action parameter bindings."""
    action_instance_ids = [action_instance.id for action_instance in SEEDED_ACTION_INSTANCES]
    action_parameter_ids = [action_parameter.id for action_parameter in SEEDED_ACTION_PARAMS]

    return [
        ActionInstanceParameterBinding(
            id=uuid4(),
            value=f"Value {i + 1}",
            action_instance_id=random.choice(action_instance_ids),
            action_parameter_id=random.choice(action_parameter_ids),
        ) for i in range(n)
    ]

SEEDED_ACTION_INSTANCE_PARAMETER_BINDINGS = generate_action_parameter_bindings()