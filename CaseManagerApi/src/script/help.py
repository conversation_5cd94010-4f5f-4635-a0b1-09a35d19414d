from __future__ import annotations

import importlib
import pkgutil
import sys
from types import ModuleType
from typing import List, Set, Tuple

from script.metadata import ScriptMeta


# ──────────────────────────────────────────────────────────────────────────
def _safe_import(dotted: str) -> ModuleType | None:
    """Import *dotted* module, swallowing any exceptions."""
    try:
        return importlib.import_module(dotted)
    except Exception as exc:
        print(f"⚠️  {dotted}: skipped ({exc})", file=sys.stderr)
        return None


def collect_script_metadata(
    root_pkg: str = "data",
) -> List[Tuple[ScriptMeta, str]]:
    """
    Recursively import every module under *root_pkg* and return
    a list of tuples: (ScriptMeta instance, origin-module string).
    """
    root = importlib.import_module(root_pkg)
    metas: List[Tu<PERSON>[ScriptMeta, str]] = []
    seen_ids: Set[int] = set()

    for modinfo in pkgutil.walk_packages(root.__path__, f"{root_pkg}."):
        mod = _safe_import(modinfo.name)
        if not mod:
            continue

        for val in mod.__dict__.values():          # ★ examine every global
            if isinstance(val, ScriptMeta) and id(val) not in seen_ids:
                metas.append((val, mod.__name__))  # keep origin module
                seen_ids.add(id(val))

    return metas
# ──────────────────────────────────────────────────────────────────────────

if __name__ == "__main__":
    metas = collect_script_metadata()
    if not metas:
        print("No scripts found.")
        sys.exit(1)

    for meta, origin_mod in metas:
        aliases = ", ".join(meta.aliases) if meta.aliases else "—"
        if getattr(meta, "usage", None):
            usage = f"python -m {origin_mod} {meta.usage}".rstrip()
        else:
            usage = "—"

        print(
            f"{meta.name:12}  {meta.description}   "
            f"(aliases: {aliases})   [usage: {usage}]"
        )
