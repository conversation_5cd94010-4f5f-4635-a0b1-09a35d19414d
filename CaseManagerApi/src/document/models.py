import os
import uuid
from datetime import datetime

from sqlalchemy import (
    UUID,
    Column,
    DateTime,
    ForeignKey,
    Integer,
    String,
    Table,
    func,
)
from sqlalchemy.orm import Mapped, mapped_column, relationship

from src.document.config import DOCUMENTS_ROOT
# from src.transaction.models import Transaction # do not remove this import
from data.database.config import Base


class ViewMode(Base):
    __tablename__ = "view_modes"

    id = mapped_column(String(50), primary_key=True, index=True, nullable=False)
    description = mapped_column(String(200), nullable=False)


documents_x_folders = Table(
    "documents_x_folders",
    Base.metadata,
    Column(
        "document_id",
        String(50),
        ForeignKey("documents.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    Column(
        "folder_id",
        UUID(as_uuid=True),
        ForeignKey("folders.id", ondelete="CASCADE"),
        primary_key=True,
    ),
)


class VirtualFolder(Base):
    __tablename__ = "folders"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        index=True,
        default=uuid.uuid4,
    )

    path_without_name: Mapped[str | None] = mapped_column(String(200), nullable=True)
    name: Mapped[str] = mapped_column(String(50), nullable=False)
    view_mode_id: Mapped[str] = mapped_column(
        String(50),
        ForeignKey(f"{ViewMode.__tablename__}.id"),
        nullable=False,
    )
    element_count: Mapped[int] = mapped_column(Integer, nullable=False, default=0)

    documents: Mapped[list["Document"]] = relationship(
        "Document",
        secondary=documents_x_folders,
        back_populates="folders",
    )

class Document(Base):
    __tablename__ = "documents"

    id: Mapped[str] = mapped_column(String(50), primary_key=True, index=True)
    case_id: Mapped[str] = mapped_column(String(100), ForeignKey("cases.id"), nullable=False)
    name: Mapped[str] = mapped_column(String(50), index=True, nullable=False)
    physical_path: Mapped[str] = mapped_column(String(500), nullable=True, default=None)

    status1: Mapped[str | None] = mapped_column(String(50), nullable=True)
    status2: Mapped[str | None] = mapped_column(String(50), nullable=True)

    size: Mapped[int] = mapped_column(Integer, nullable=False)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )

    folders: Mapped[list["VirtualFolder"]] = relationship(
        "VirtualFolder",
        secondary=documents_x_folders,
        back_populates="documents",
    )

    actions = relationship(
        "ActionInstance",
        back_populates="document",
        cascade="all, delete-orphan",
    )

    @property
    def path(self) -> str:
        if self.physical_path is None:
            return os.path.join(DOCUMENTS_ROOT, str(self.case_id), str(self.name))

        return self.physical_path
