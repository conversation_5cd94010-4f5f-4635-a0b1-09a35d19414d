import os
from typing import List, Sequence

from fastapi import APIRouter, HTTPException, status, Depends
from fastapi.responses import FileResponse
from pydantic import ByteSize
from sqlalchemy import and_, select
from sqlalchemy.orm import Session

from src.auth.utils import get_current_active_user
from data.database.config import get_db
from src.document.models import VirtualFolder, Document, documents_x_folders, ViewMode
from src.document.schemas import (
    FolderData,
    ParentPathInput,
    FolderItemsResponse,
    PagedFolderChildrenInput,
    FileData,
    FileDataWithPath,
    ErrorResponse,
    FileDownloadInput,
    SearchFilesInput, FilePathsResponse, FilePathsRequest, ViewModeData,
)

router = APIRouter(
    prefix="/api",
    tags=["document"],
    dependencies=[Depends(get_current_active_user)],
)

@router.get(
    path="/meta",
    response_model=FolderData,
    summary="Get folder metadata",
    description="Fetch metadata for a folder without loading its children",
    operation_id="getFolderMetadata")
async def get_folder_metadata(
    params: ParentPathInput = Depends(),
    db: Session = Depends(get_db),
) -> FolderData:
    """
    Return basic metadata for the folder given by `params.path`.
    """

    folder: VirtualFolder | None = (
        db.execute(
            select(VirtualFolder)
            .where(VirtualFolder.view_mode_id == params.view_mode)
            .where(VirtualFolder.path_without_name == params.path_without_name)
            .where(VirtualFolder.name == params.name)
        )
        .scalars()
        .first()
    )

    if folder is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Folder not found.",
        )

    return FolderData(
        name=folder.name,
        total_element_count=folder.element_count,
    )


@router.get(
    path="/children",
    response_model=FolderItemsResponse,
    summary="List immediate children of a folder",
    description="Returns the immediate children (both files and directories) of the specified folder",
    operation_id="listFolderChildren")
async def list_folder_children(
        params: PagedFolderChildrenInput = Depends(),
        db: Session = Depends(get_db),
) -> FolderItemsResponse:
    """
    Return the *immediate* children (folders + files) that live under
    the folder identified by `params.path`, with offset + pagination.
    """
    params.path = '' if params.path == '/' else params.path
    folder_query = select(VirtualFolder).filter_by(
        view_mode_id=params.view_mode,
        path_without_name=params.path_without_name,
        name=params.name
    )
    folder = db.scalar(folder_query)

    if folder is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Folder not found.",
        )
    subfolders: Sequence[VirtualFolder] = (
        db.scalars(
            select(VirtualFolder).filter_by(
                view_mode_id=params.view_mode,
                path_without_name=params.path
            ).order_by(VirtualFolder.name)
        )
        .all()
    )
    documents = (
        db.scalars(
            select(Document)
            .join(documents_x_folders)
            .where(documents_x_folders.c.folder_id == folder.id)
            .order_by(Document.created_at.desc())
        )
        .all()
    )

    children: list[FolderData | FileData] = (
        [FolderData(name=sf.name, total_element_count=sf.element_count) for sf in subfolders] +
        [FileData(name=d.name, size=d.size or 0, id=d.id, creation_date=d.created_at) for d in documents]
    )

    total_count = len(children)

    start = (params.page - 1) * params.page_size
    end = start + params.page_size

    offset_children = children[params.offset:] if params.offset < total_count else []
    return FolderItemsResponse(
        count=total_count,
        page=params.page,
        page_size=params.page_size,
        offset=params.offset,
        results=offset_children[start:end],
    )


@router.get(
    path="/files",
    response_model=List[FileDataWithPath],
    summary="Search files",
    description="Search for files based on creation date and view mode",
    operation_id="searchFiles"
)
async def search_files(params: SearchFilesInput = Depends(), db: Session = Depends(get_db)) -> List[FileDataWithPath]:

    filters = (
        Document.created_at >= params.from_creation_date  if params.from_creation_date else None,
        Document.created_at <= params.to_creation_date if params.to_creation_date else None,
        Document.case_id == params.case_id if params.case_id else None,
        Document.status1 == params.status if params.status else None,
        VirtualFolder.view_mode_id == params.view_mode if params.view_mode else None,
    )
    filters = [f for f in filters if f is not None]

    query = (
        select(Document, VirtualFolder.path_without_name, VirtualFolder.name)
        .join(VirtualFolder, Document.folders)
        .where(and_(*filters))
    )

    rows = db.execute(query).all()

    return [
        FileDataWithPath(
            path=f"{path_without_name}/{name}/{doc.name}",
            size=ByteSize(doc.size),
            id=doc.id,
            view_mode=params.view_mode,)
        for doc, path_without_name, name in rows
    ]

@router.get(
    path="/download",
    summary="Download a file",
    description="Download a file by path with specified mode",
    operation_id="downloadFile",
    response_class=FileResponse,
    responses={
        200: {
            "description": "PDF file",
            "content": {
                "application/pdf": {
                    "schema": {
                        "type": "string",
                        "format": "binary"
                    }
                }
            }
        },
        404: {
            "description": "File not found",
            "model": ErrorResponse
        }
    }
)
async def download_file(params: FileDownloadInput = Depends(), db: Session = Depends(get_db)) -> FileResponse:
    document: Document | None = db.scalar(select(Document).where(Document.id == params.document_id))
    if document is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found.",
        )

    if not os.path.exists(document.path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found.",
        )

    return FileResponse(
        path=document.path,
        filename=str(document.name),
        media_type="application/pdf"
    )



# get all view modes
@router.get(
    path="/view-modes",
    summary="Get all view modes",
    description="Get all view modes",
    operation_id="getViewModes",
    response_model=list[ViewModeData],
)
async def get_view_modes(db: Session = Depends(get_db)) -> list[ViewModeData]:
    view_modes = db.scalars(select(ViewMode)).all()
    return [ViewModeData.model_validate(vm) for vm in view_modes]


@router.post(
    "/action1",
    response_model=FilePathsResponse,
    summary="Filter file paths (Action 1)",
    description="Accepts a JSON body with a list of file paths under `filePaths`. Returns a filtered list of file paths "
                "(could be smaller than input).",
    operation_id="performAction1",
)
async def perform_action1(request: FilePathsRequest) -> FilePathsResponse:
    """
    POST endpoint that accepts a list of file paths and returns a filtered list (could be smaller than input).
    """
    file_paths = request.filePaths

    try:
        if len(file_paths) > 2:
            file_paths.pop(2)
    except IndexError:
        pass

    return FilePathsResponse(filePaths=file_paths)

@router.post(
    "/action2",
    response_model=FilePathsResponse,
    summary="Filter file paths (Action 2)",
    description="Accepts a JSON body with a list of file paths under `filePaths`. Returns a filtered list of file paths "
                "(could be smaller than input). This endpoint is similar to Action 1.",
    operation_id="performAction2",
)
async def perform_action2(request: FilePathsRequest) -> FilePathsResponse:
    """
    POST endpoint that accepts a list of file paths and returns a filtered list (could be smaller than input).
    """
    file_paths = request.filePaths

    try:
        if len(file_paths) > 2:
            file_paths.pop(2)
    except IndexError:
        pass

    return FilePathsResponse(filePaths=file_paths)
