from typing import Literal, Annotated, List
from typing import Literal, Annotated, List
from datetime import datetime

from pydantic import Field, ByteSize, Discriminator, ConfigDict

from src.schemas import CamelModel

BaseModel = CamelModel

class ParentPathInput(BaseModel):
    path: str = Field("/", description="Folder path (e.g. `/folder1/folder2`). Root is `/` or omitted.")
    view_mode: str = Field(None, description="View mode to organize results")
    @property
    def path_without_name(self):
        if self.path in ('', '/'):
            return None
        return self.path.rsplit("/", 1)[0]
    @property
    def name(self):
        if self.path in ('', '/'):
            return ''
        return self.path.rsplit("/", 1)[-1]

class FileData(BaseModel):
    id: str
    name: str
    size: ByteSize
    type: Literal["pdf"] = "pdf"
    creation_date: datetime

    model_config = ConfigDict(from_attributes=True)

class FileDataWithPath(ParentPathInput):
    id: str
    size: ByteSize
    type: Literal["pdf"] = "pdf"

class FolderData(BaseModel):
    name: str
    total_element_count: int
    type: Literal["folder"] = "folder"

    model_config = ConfigDict(from_attributes=True)

FileSystemEntry = Annotated[FileData | FolderData, Discriminator("type")]

class FolderItemsResponse(BaseModel):
    count: int
    page: int
    page_size: int
    offset: int
    results: List[FileSystemEntry]

class PagedFolderChildrenInput(ParentPathInput):
    page: int = Field(1, description="Page number (1-based). Defaults to 1.")
    page_size: int = Field(20, description="Number of elements per page. Defaults to 20.")
    offset: int = Field(0, description="Number of elements to skip from the beginning. Defaults to 0.")

class FileDownloadInput(BaseModel):
    document_id: str

class ErrorResponse(BaseModel):
    detail: str

class SearchFilesInput(BaseModel):
    from_creation_date: datetime| None = Field(
        None, description="Start date for file creation"
    )
    to_creation_date: datetime | None = Field(
        None, description="End date for file creation"
    )
    status: str | None = Field(None, description="Status of the file")
    case_id: str | None = Field(None, description="Case ID associated with the file")
    view_mode: str = Field(None, description="View mode to organize results")

class FilePathsRequest(BaseModel):
    filePaths: List[str]

class FilePathsResponse(BaseModel):
    filePaths: List[str]

class ViewModeData(BaseModel):
    id: str
    description: str

    model_config = ConfigDict(from_attributes=True)