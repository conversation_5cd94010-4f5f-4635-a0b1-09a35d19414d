import random
from datetime import datetime, timedelta

from case.seed import SEEDED_CASES
from src.document.models import ViewMode

SEEDED_VIEW_MODES = [
    ViewMode(id="case", description="Organise by case id"),
    ViewMode(id="date", description="Organise by year / month"),
    ViewMode(id="status", description="Organise by status1 / status2"),
    ViewMode(id="task", description="Organise by task"),
    ViewMode(id="custom", description="Custom view mode"),
]

def generate_seeded_docs(n: int = 5000):
    """Return *n* pseudo-random documents."""
    case_ids = [c.id for c in SEEDED_CASES]
    return [
        {
            "id": f"DOC-{i:04d}",
            "case_id": random.choice(case_ids),
            "name": f"DOC-{i:04d}.pdf",
            "status1": random.choice(["new", "in_review", "approved", "rejected"]),
            "status2": random.choice(["draft", "final"]),
            "size": random.randint(10_000, 100_000),
            "created_at": datetime.now() - timedelta(days=random.randint(0, 60)),
        }
        for i in range(1, n + 1)
    ]

SEEDED_DOCS = generate_seeded_docs()