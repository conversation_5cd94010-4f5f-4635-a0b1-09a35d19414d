import asyncio
from contextlib import asynccontextmanager
from pathlib import Path

from fastapi.middleware.cors import CORSMiddleware
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.responses import FileResponse
from starlette.websockets import WebSocket, WebSocketDisconnect

from src import auth,  document, action, case
from src.kafka.kafka_consumer import start_consumer
from src.websocket import manager

routers = [
    auth.router,
    document.router,
    case.action_instances.router,
    case.transaction.router,
    case.email.router,
    case.debtor.router,
    action.router,
]

@asynccontextmanager
async def lifespan(app: FastAPI):
    kafka_task = asyncio.create_task(start_consumer())
    print("Kafka consumer started in background")

    yield

    try:
        kafka_task.cancel()
        await kafka_task
    except asyncio.CancelledError:
        print("Kafka consumer task cancelled")

app = FastAPI(lifespan=lifespan)

app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:4200",
        "http://**********:4200",
        "http://**********:8000",
        "http://localhost:8000",
        "http://127.0.0.1:8000",
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "PATCH", "DELETE"],
    allow_headers=["Content-Type", "Authorization"],
)

for router in routers:
    app.include_router(router)

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    try:
        while await websocket.receive_text():
            pass
    except WebSocketDisconnect:
        manager.disconnect(websocket)


spa_dir = Path(__file__).parent / "static" / "browser"
index_file = spa_dir / "index.html"
@app.get("/{full_path:path}", include_in_schema=False)
async def spa_router(request: Request, full_path: str):
    if request.url.path.startswith(("/api", "/ws")):
        raise HTTPException(status_code=404)

    candidate = spa_dir / full_path.lstrip("/")
    if candidate.is_file():
        return FileResponse(candidate)

    return FileResponse(index_file)