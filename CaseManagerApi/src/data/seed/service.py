import uuid
from collections.abc import Mapping

from sqlalchemy import orm, select, delete, Table, insert

from src.action.models import actions_x_parameters
from src.case.action_instances.seed import SEEDED_ACTION_STATUSES, SEEDED_ACTION_INSTANCES, \
    SEEDED_ACTION_INSTANCE_PARAMETER_BINDINGS
from src.action.seed import SEEDED_ACTION_GROUPS, SEEDED_ACTIONS, SEEDED_ACTION_PARAMS, SEEDED_PARAMETER_TYPES, \
    SEEDED_ACTIONS_X_PARAMETERS
from src.auth.models import User, UserSetting
from src.auth.seed import DEFAULT_ADMIN
from src.auth.utils import get_password_hash
from src.case.debtor.models import Debtor
from src.case.debtor.seed import SEEDED_DEBTORS
from src.case.email.seed import SEEDED_EMAILS
from src.case.models import Case
from src.case.seed import SEEDED_CASES
from src.case.transaction.seed import SEEDED_TRANSACTION_TYPES, SEEDED_TRANSACTION_STATUSES, SEEDED_CURRENCIES, \
    SEEDED_TRANSACTIONS
from src.document.models import Document, VirtualFolder, documents_x_folders
from src.document.seed import SEEDED_DOCS, SEEDED_VIEW_MODES


def ensure_folder(
    db: orm.Session,
    view_mode_id: str,
    path_without_name: str | None,
    name: str,
) -> VirtualFolder:
    """
    Return an existing folder or create a new one.
    If the new folder is an *immediate* child of the root (i.e. its
    parent is the invisible “/”), bump the root folder’s element_count.
    """
    # Try to fetch an existing folder first
    query = select(VirtualFolder).filter_by(
        view_mode_id=view_mode_id,
        path_without_name=path_without_name,
        name=name,
    )
    existing: VirtualFolder | None = db.scalars(query).one_or_none()
    if existing:
        return existing

    folder = VirtualFolder(
        view_mode_id=view_mode_id,
        path_without_name=path_without_name,
        name=name,
        element_count=0,
    )
    db.add(folder)

    if path_without_name is None:
        return folder

    if path_without_name == "":
        root = db.scalar(
            select(VirtualFolder).filter_by(
                view_mode_id=view_mode_id,
                path_without_name=None,
                name="",
            )
        )
        if root:
            root.element_count += 1
        return folder
    parent_path, parent_name = path_without_name.rsplit("/", 1)
    # if parent_path == "":
    #     parent_path = None
    parent = db.scalar(
        select(VirtualFolder).filter_by(
            view_mode_id=view_mode_id,
            path_without_name=parent_path,
            name=parent_name,
        )
    )
    if parent:
        parent.element_count += 1

    return folder


def create_doc(db: orm.Session, doc_info) -> None:
    ensure_folder(db, "case", None, "")
    # a) CASE  view-mode: {root}/{case_id}/documents
    case_folder = ensure_folder(db, "case", "", doc_info["case_id"])
    # case_folder.element_count += 1

    # b) DATE view-mode: {root}/{year}/{month}/documents
    year = str(doc_info["created_at"].year)
    month = "%02d" % doc_info["created_at"].month
    ensure_folder(db, "date", None, "")
    ensure_folder(db, "date", "", year)
    # year_folder.element_count += 1
    month_folder = ensure_folder(db, "date", f"/{year}", month)
    # month_folder.element_count += 1

    # c) STATUS view-mode: {root}/{status1}/{status2}/documents
    stat1 = doc_info["status1"] or "undefined"
    stat2 = doc_info["status2"] or "undefined"
    ensure_folder(db, "status", None, "")
    ensure_folder(db, "status", "", stat1)
    # stat1_folder.element_count += 1
    stat2_folder = ensure_folder(db, "status", f"/{stat1}", stat2)
    # stat2_folder.element_count += 1
    doc = Document(**doc_info, updated_at=doc_info["created_at"])
    db.add(doc)
    db.flush()

    doc.folders.extend([case_folder, month_folder, stat2_folder])

    # bump the counters of the folders that now contain a file
    case_folder.element_count += 1
    month_folder.element_count += 1
    stat2_folder.element_count += 1


def create_documents(db, data):
    exists = db.scalars(select(Document)).first()
    if exists:
        print("Documents already exist, skipping creation")
        return
    print("Seeding documents and folders...")
    for doc_info in data:
        create_doc(db, doc_info)
    db.commit()

def create_admin_user(db: orm.Session) -> User | None:
    """Create default admin user if it doesn't exist."""
    exists = db.scalars(select(User)).first()
    if exists:
        print("Admin user already exists, skipping creation")
        return None
    admin = User(
        id=uuid.uuid4(),
        username=DEFAULT_ADMIN["username"],
        email=DEFAULT_ADMIN["email"],
        hashed_password=get_password_hash(DEFAULT_ADMIN["password"]),
        is_active=True,
    )
    db.add(admin)
    db.commit()
    return admin

def bulk_create(db, data: list, target=None):
    """
    Insert a list of ORM instances **or** plain dictionaries.

    Parameters
    ----------
    db      : SQLAlchemy Session
    data    : list[Union[Base, dict]]
              • ORM instances     → pass them directly
              • plain dicts       → pass the Table or mapped class via *target*
    target  : Optional[Table | DeclarativeModel]
              The table / model to insert into when *data* contains dictionaries.

    Examples
    --------
    # classic usage – unchanged
    bulk_create(db, [User(id=uuid4(), name="Alice")])

    # seeding the many-to-many table
    bulk_create(db, SEEDED_ACTIONS_X_PARAMETERS, actions_x_parameters)
    """
    if not data:
        return

    # Work out where we are inserting
    if target is None:
        if hasattr(data[0], "__table__"):
            target = type(data[0])
        else:
            raise ValueError("When *data* contains plain dicts you must supply 'target'.")

    is_table   = isinstance(target, Table)
    is_mapping = isinstance(data[0], Mapping)

    # Skip if the table already has rows (idempotent seeding)
    if is_table:
        if db.execute(select(target).limit(1)).first():
            print(f"{target.name} already has rows, skipping creation")
            return
        print(f"Seeding table '{target.name}'")
    elif db.scalars(select(target).limit(1)).first():
        print(f"{target.__name__} already exists, skipping creation")
        return
    else:
        print(f"Seeding table for model '{target.__name__}'")

    # Do the insert
    if is_table:
        db.execute(insert(target), data)
    elif is_mapping:
        db.bulk_insert_mappings(target, data)
    else:
        db.bulk_save_objects(data)

    db.commit()

STATIC_DATAS = [
    SEEDED_VIEW_MODES,
    SEEDED_ACTION_GROUPS,
    SEEDED_TRANSACTION_TYPES,
    SEEDED_TRANSACTION_STATUSES,
    SEEDED_CURRENCIES,
    SEEDED_ACTION_STATUSES,
    SEEDED_PARAMETER_TYPES,
]

RANDOM_DATAS = [
    SEEDED_TRANSACTIONS,
    SEEDED_ACTIONS,
    SEEDED_ACTION_PARAMS,
    SEEDED_ACTION_INSTANCES,
    SEEDED_EMAILS,
    SEEDED_ACTION_INSTANCE_PARAMETER_BINDINGS,
]

def upgrade(bind) -> None:
    db = orm.Session(bind=bind)

    for data in STATIC_DATAS: bulk_create(db, data)
    bulk_create(db, SEEDED_DEBTORS)
    bulk_create(db, SEEDED_CASES)
    create_documents(db, SEEDED_DOCS)
    for data in RANDOM_DATAS: bulk_create(db, data)
    bulk_create(db, SEEDED_ACTIONS_X_PARAMETERS, actions_x_parameters)
    admin = create_admin_user(db)
    bulk_create(db, UserSetting.for_user(admin.id))

def delete_model_data(db, model):
    table_name = model.name if isinstance(model, Table) else model.__tablename__
    print(f"Deleting data for table '{table_name}'")
    db.execute(delete(model))
    db.commit()

def downgrade(bind) -> None:
    """Downgrade schema."""
    db = orm.Session(bind=bind)
    print("Unseeding data...")
    delete_model_data(db, actions_x_parameters)

    for data in reversed(RANDOM_DATAS): delete_model_data(db, type(data[0]))
    for model in [documents_x_folders, Document, VirtualFolder, Case]: delete_model_data(db, model)
    delete_model_data(db, Case)
    delete_model_data(db, Debtor)

    for data in reversed(STATIC_DATAS): delete_model_data(db, type(data[0]))

    delete_model_data(db, UserSetting)
    delete_model_data(db, User)