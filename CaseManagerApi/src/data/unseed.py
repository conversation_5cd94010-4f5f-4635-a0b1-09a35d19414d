from sqlalchemy import create_engine

from data.database.config import CASE_MANAGER_DB_URL
from data.seed.service import downgrade
from script.metadata import ScriptMeta

meta = ScriptMeta(
    name="unseed",
    description="Removes sample data from the database, but keeps the schema",
)
if __name__ == "__main__":
    engine = create_engine(CASE_MANAGER_DB_URL, isolation_level="SERIALIZABLE")
    downgrade(bind=engine.connect())