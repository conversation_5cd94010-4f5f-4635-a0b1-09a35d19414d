from data.create import create_db
from data.database.config import CASE_MANAGER_DB_URL, DEFAULT_POSTGRES_DB_URL
from data.drop import drop_db
from script.metadata import ScriptMeta

meta = ScriptMeta(
    name="reset",
    description="Drops the entire case_manager database, then creates it again",
)

if __name__ == "__main__":
    drop_db(DEFAULT_POSTGRES_DB_URL, CASE_MANAGER_DB_URL)
    create_db(DEFAULT_POSTGRES_DB_URL, CASE_MANAGER_DB_URL)