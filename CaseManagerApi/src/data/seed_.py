from sqlalchemy import create_engine

from data.database.config import CASE_MANAGER_DB_URL
from data.seed.service import upgrade
from script.metadata import ScriptMeta

meta = ScriptMeta(
    name="seed",
    description="Seeds the database with sample data",
)

if __name__ == "__main__":
    engine = create_engine(CASE_MANAGER_DB_URL, isolation_level="SERIALIZABLE")
    upgrade(bind=engine.connect())