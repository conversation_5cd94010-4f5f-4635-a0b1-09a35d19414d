from typing import Literal
from urllib.parse import Parse<PERSON><PERSON><PERSON>, urlparse, urlunparse
import time

from sqlalchemy import create_engine, text

def replace_postgres_db_name(conn_str: str, new_db_name: str) -> Literal[b""]:
    """
    Replaces the database name in a PostgreSQL connection string with a new one,
    preserving all other components.

    :param conn_str: Original PostgreSQL connection string
    :param new_db_name: New database name to use
    :return: Modified connection string with the new database name
    """
    parsed: ParseResult = urlparse(conn_str)

    if not parsed.scheme.startswith("postgresql"):
        raise ValueError("Not a valid PostgreSQL connection string.")

    updated = parsed._replace(path=f'/{new_db_name}')
    return urlunparse(updated)


def get_postgres_db_name(conn_str: str) -> str:
    """
    Extracts the database name from a PostgreSQL connection string.

    Example:
        postgresql://user:pass@localhost:5432/mydatabase -> "mydatabase"
    """
    parsed = urlparse(conn_str)
    if parsed.scheme.startswith("postgresql"):
        return parsed.path.lstrip('/')
    raise ValueError("Not a valid PostgreSQL connection string.")

def drop_db(default_postgres_db_url: str, case_manager_db_url: str):
    engine = create_engine(default_postgres_db_url, isolation_level="AUTOCOMMIT")
    db_name = get_postgres_db_name(case_manager_db_url)

    print("Dropping database...")
    with engine.connect() as conn:
        conn.execute(text(f"""
            SELECT pg_terminate_backend(pg_stat_activity.pid)
            FROM pg_stat_activity
            WHERE pg_stat_activity.datname = '{db_name}'
            AND pid <> pg_backend_pid();
        """))
        time.sleep(1)
        print(f"Dropping database {db_name}...")
        conn.execute(text(f"DROP DATABASE IF EXISTS {db_name};"))

    print("Database dropped.")
def create_db(default_postgres_db_url: str, case_manager_db_url: str):
    parsed = urlparse(case_manager_db_url)
    target_db_name = parsed.path.lstrip("/")

    control_engine = create_engine(default_postgres_db_url, isolation_level="AUTOCOMMIT")

    with control_engine.connect() as conn:
        print(f"Creating database '{target_db_name}'...")
        conn.execute(text(f"CREATE DATABASE {target_db_name};"))
        print("Database created.")
