import os

from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

from data.database.service import replace_postgres_db_name

CASE_MANAGER_DB_URL = os.getenv(
    "PSSWEB_DATABASE_URL",
    "postgresql://postgres:postgres@localhost:5432/case_manager",
)

DEFAULT_POSTGRES_DB_URL = replace_postgres_db_name(conn_str=CASE_MANAGER_DB_URL, new_db_name='postgres')

engine = create_engine(CASE_MANAGER_DB_URL, echo=True)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()