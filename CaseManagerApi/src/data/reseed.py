import sys

from sqlalchemy import create_engine

from data.database.config import CASE_MANAGER_DB_URL, Base, DEFAULT_POSTGRES_DB_URL
from data.create import create_db
from data.drop import drop_db
from data.seed.service import downgrade, upgrade
from script.metadata import <PERSON>riptMeta
meta = ScriptMeta(
    name="reseed",
    description=(
        "Reseeds the database with sample data. If called with the 'full' flag, "
        "then drops the entire database, migrates the schema, and seeds the "
        "database with sample data."
    ),
    usage="[--full] | [-full] | [-f]",
)

if __name__ == "__main__":
    if len(sys.argv) > 2:
        print("Usage: python -m reseed [-full | --full | -f]")
        sys.exit(1)
    elif len(sys.argv) == 1:
        engine = create_engine(CASE_MANAGER_DB_URL, isolation_level="SERIALIZABLE")
        bind = engine.connect()
        downgrade(bind)
        upgrade(bind)
    elif sys.argv[1] == "--full" or sys.argv[1] == "-full" or sys.argv[1] == "-f":
        drop_db(DEFAULT_POSTGRES_DB_URL, CASE_MANAGER_DB_URL)
        create_db(DEFAULT_POSTGRES_DB_URL, CASE_MANAGER_DB_URL)
        engine = create_engine(CASE_MANAGER_DB_URL, isolation_level="SERIALIZABLE")
        Base.metadata.create_all(bind=engine)
        upgrade(bind=engine.connect())
