import uuid
from typing import Any

from pydantic import EmailStr, Field
from src.schemas import CamelModel

BaseModel = CamelModel
class TokenResponse(BaseModel):
    """JWT token response model using FastAPI built-in patterns"""
    access_token: str = Field(..., description="JWT access token")
    token_type: str = Field(..., description="Token type (bearer)")


class UserCreateRequest(BaseModel):
    """User creation request model using FastAPI built-in patterns"""
    username: str = Field(..., description="Unique username")
    email: EmailStr = Field(..., description="User's email address")
    password: str = Field(..., description="User's password (will be hashed)")


class UserResponse(BaseModel):
    """User response model using FastAPI built-in patterns"""
    id: uuid.UUID = Field(..., description="Unique user ID")
    username: str = Field(..., description="Unique username")
    email: str = Field(..., description="User's email address")
    is_active: bool = Field(..., description="Whether the user account is active")

    class Config:
        from_attributes = True


class UpdateUserSettingsInput(BaseModel):
    """Update user settings request model using FastAPI built-in patterns"""
    settings: dict[str, Any] = Field(..., description="User settings to update or add")

class UserSettingItemData(BaseModel):
    """User setting item response model using FastAPI built-in patterns"""
    key: str = Field(..., description="Setting key")
    value: Any = Field(..., description="Setting value")
    description: str = Field(..., description="Setting description")

class UserSettingsData(BaseModel):
    """User settings response model using FastAPI built-in patterns"""
    id: uuid.UUID = Field(..., description="Unique user ID")
    settings: dict[str, UserSettingItemData] = Field(..., description="User settings")
