import uuid
from dataclasses import dataclass

from sqlalchemy import Column, Integer, String, Boolean, DateTime, func, ForeignKey, UUID, Float
from sqlalchemy.orm import relationship, Mapped, mapped_column

from data.database.config import Base

@dataclass
class SettingTemplate:
    key: str
    default_value: object
    type: type
    description: str = ""

    def to_setting(self, user_id: uuid.UUID):
        return UserSetting(
            id=uuid.uuid4(),
            user_id=user_id,
            value=str(self.default_value),
            key=self.key,
        )

    @classmethod
    def description_of(cls, setting_key: str) -> str:
        return next((s.description for s in cls.all() if s.key == setting_key), "")

    __ALL = []
    @classmethod
    def all(cls):
        if cls.__ALL:
            return cls.__ALL
        __ALL = [
            cls("thumbnailSizePx", 450, int, "Thumbnail width in pixels (50 – 1000)."),
            cls("language", "en", str),
            cls("showThumbnails", True, bool),
            cls("theme", "light", str),
            cls("documentTabsVertical", True, bool),
            cls("thumbnailAspectRatio", 4 / 3, float, "Width / Height ratio (e.g. 1 for square)."),
            cls("treeMobileMenuThreshold", 300, int, "Viewport width in px below which mobile menu is used above the document tree."),
            cls("enableVirtualScrolling", False, bool, description="Turn this off if the document tree is jumping around upon expanding and collapsing folders!"),
            cls("treeNodeHeight", 34, int, description="How tall the tree nodes should be in px. Set this to a lower value if you want a more compact tree!"),
        ]
        return __ALL


class UserSetting(Base):
    __tablename__ = "user_settings"

    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, index=True)
    user_id: Mapped[UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False,
    )
    key = mapped_column(String(100), nullable=False)
    value = mapped_column(String(100), nullable=False)

    user: Mapped["User"]  = relationship("User", back_populates="settings")

    @classmethod
    def for_user(cls, user_id) -> list["UserSetting"]:
        return [ s.to_setting(user_id) for s in SettingTemplate.all() ]

    def typed_value(self):
        template_for_self = next((s for s in SettingTemplate.all() if s.key == self.key), None)
        if template_for_self is None:
            return None
        cast_type = template_for_self.type
        if cast_type is bool:
            return bool(self.value.lower() in ("true", "yes", "1"))
        return cast_type(self.value)

class User(Base):
    __tablename__ = "users"

    id = mapped_column(UUID, primary_key=True, index=True)
    # id = mapped_column(Integer, primary_key=True, index=True)
    username = mapped_column(String(50), unique=True, index=True)
    email = mapped_column(String(100), unique=True, index=True)
    hashed_password = mapped_column(String(200))
    is_active = mapped_column(Boolean, default=True)
    created_at = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at = mapped_column(DateTime(timezone=True), onupdate=func.now())

    settings: Mapped[list[UserSetting]] = relationship("UserSetting", back_populates="user")