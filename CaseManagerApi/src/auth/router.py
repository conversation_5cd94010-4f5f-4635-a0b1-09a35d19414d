import uuid
from datetime import <PERSON><PERSON><PERSON>
from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session

from src.auth import config
from src.auth.models import User, UserSetting, SettingTemplate
from src.auth.schemas import TokenResponse, UserResponse, UserCreateRequest, UserSettingsData, \
    UpdateUserSettingsInput, UserSettingItemData
from data.database.config import get_db
from src.auth.utils import (
    authenticate_user,
    create_access_token,
    get_current_active_user,
    get_password_hash,
    get_user,
)

router = APIRouter(prefix="/api", tags=["authentication"])


@router.post(
    "/token",
    response_model=TokenResponse,
    summary="Login and get access token",
    description="Authenticate with username and password to receive a JWT token for accessing protected endpoints.",
    operation_id="loginForAccessToken",
    responses={
        200: {
            "description": "Successfully authenticated",
            "content": {
                "application/json": {
                    "example": {"access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "token_type": "bearer"}
                }
            }
        },
        401: {
            "description": "Authentication failed",
            "content": {
                "application/json": {
                    "example": {"detail": "Incorrect username or password"}
                }
            }
        }
    }
)
async def login_for_access_token(
    form_data: Annotated[OAuth2PasswordRequestForm, Depends()],
    db: Annotated[Session, Depends(get_db)]
) -> TokenResponse:
    """
    Authenticate a user and generate a JWT access token.

    - **username**: The user's username
    - **password**: The user's password

    Returns a JWT token that can be used to authenticate future requests.
    The token expires after the configured time (default: 30 minutes).
    """
    user = authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=config.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    return TokenResponse(access_token=access_token, token_type="bearer")


@router.post(
    "/users",
    response_model=UserResponse,
    summary="Register a new user",
    description="Create a new user account with username, email, and password.",
    operation_id="createUser",
    status_code=status.HTTP_201_CREATED,
    responses={
        201: {
            "description": "User successfully created",
            "content": {
                "application/json": {
                    "example": {"id": 1, "username": "newuser", "email": "<EMAIL>", "is_active": True}
                }
            }
        },
        400: {
            "description": "Registration failed",
            "content": {
                "application/json": {
                    "example": {"detail": "Username already registered"}
                }
            }
        }
    }
)
async def create_user(
    user: UserCreateRequest,
    db: Annotated[Session, Depends(get_db)]
) -> UserResponse:
    """
    Register a new user in the system.

    - **username**: Unique username for the new account
    - **email**: Valid email address (must be unique)
    - **password**: Password for the account

    Returns the created user information (without the password).
    """
    db_user = get_user(db, username=user.username)
    if db_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username already registered"
        )

    # Check if email already exists
    email_exists = db.query(User).filter(User.email == user.email).first()
    if email_exists:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )

    # Create new user
    hashed_password = get_password_hash(user.password)
    db_user = User(
        username=user.username,
        email=user.email,
        hashed_password=hashed_password,
        is_active=True,
        settings=UserSetting.for_user(user.id),
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)

    return UserResponse(
        id=db_user.id,
        username=db_user.username,
        email=db_user.email,
        is_active=db_user.is_active
    )


@router.get(
    "/users/me",
    response_model=UserResponse,
    summary="Get current user information",
    description="Retrieve information about the currently authenticated user.",
    operation_id="getCurrentUser",
    responses={
        200: {
            "description": "Current user information",
            "content": {
                "application/json": {
                    "example": {"id": 1, "username": "testuser", "email": "<EMAIL>", "is_active": True}
                }
            }
        },
        401: {
            "description": "Not authenticated",
            "content": {
                "application/json": {
                    "example": {"detail": "Could not validate credentials"}
                }
            }
        }
    }
)
async def read_users_me(
    current_user: Annotated[User, Depends(get_current_active_user)]
) -> UserResponse:
    """
    Get information about the currently authenticated user.

    This endpoint requires authentication with a valid JWT token.
    The token must be provided in the Authorization header as a Bearer token.

    Returns the user information associated with the provided token.
    """
    return UserResponse(
        id=current_user.id,
        username=current_user.username,
        email=current_user.email,
        is_active=current_user.is_active
    )


@router.patch(
    "/users/me/update_settings",
    response_model=UserSettingsData,
    summary="Update current user settings",
    description="Update the settings of the currently authenticated user.",
    operation_id="updateUserSettings",
    responses={
        200: {
            "description": "User settings updated",
            "content": {
                "application/json": {}
            }
        }
    }
)
async def update_settings(
        params: UpdateUserSettingsInput,
        user: Annotated[User, Depends(get_current_active_user)],
        db: Annotated[Session, Depends(get_db)]) -> UserSettingsData:
    settings: list[UserSetting] = user.settings
    for key, new_value in params.settings.items():
        setting = next((s for s in settings if s.key == key), None)
        if setting is None:
            setting = UserSetting(
                id=uuid.uuid4(),
                user_id=user.id,
                key=key,
                value=new_value
            )
            settings.append(setting)
            db.add(setting)
        setting.value = new_value
    db.commit()
    db.refresh(user)

    return await get_settings(user)

# get user settings
@router.get(
    "/users/me/settings",
    response_model=UserSettingsData,
    summary="Get current user settings",
    description="Retrieve the settings of the currently authenticated user.",
    operation_id="getUserSettings",
    responses={
        200: {
            "description": "User settings retrieved",
            "content": {}
        }
    }
)
async def get_settings(current_user: Annotated[User, Depends(get_current_active_user)]) -> UserSettingsData:
    return UserSettingsData(
        id=current_user.id,
        settings={
            s.key: UserSettingItemData(
                    key=s.key,
                    value=s.typed_value(),
                    description=SettingTemplate.description_of(s.key),)
            for s in current_user.settings
        }
    )