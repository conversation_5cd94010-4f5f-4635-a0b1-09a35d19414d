from pydantic import BaseModel

def to_camel(string: str) -> str:
    """snake_case → camelCase"""
    parts = string.split('_')
    return parts[0] + ''.join(word.title() for word in parts[1:])

class CamelModel(BaseModel):
    """
    Base for all schemas that should appear as camelCase
    in OpenAPI and on the wire.
    """
    model_config = {
        "alias_generator": to_camel,
        "populate_by_name": True,
    }
